from collections import deque
import logging
import signal
import json
import os
from multiprocessing import Process, Manager, Value, Queue, Event, set_start_method
import uvicorn
import threading

# 프로세스 함수들 import
from pipeline.receiver import receive_worker
from pipeline.parser import parse_and_save_worker, init_error_counter
from pipeline.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pipeline.relay import relay
from pipeline.monitor import MonitorConfig, monitor_worker
from utils.config_parser import get_config, get_device_config
from utils.redis_client import redis_mgr
from api.app import app
from utils import config


logger = logging.getLogger(__name__)


def restore_error_counter_from_snapshot(counter_proxy, logs_proxy):
    """
    스냅샷에서 에러 카운터와 로그 복원 (init_error_counter 호출 전)

    Returns:
        bool: 복원 성공 여부
    """
    snapshot_file = "config/errors/error_snapshot.json"

    try:
        with open(snapshot_file, "r") as f:
            snapshot = json.load(f)

        # 버전 호환성 체크
        version = snapshot.get("version", "legacy")
        created_at = snapshot.get("created_at", snapshot.get("timestamp", 0))

        # counter_proxy에 복원
        counter_data = snapshot.get("counter", {})
        for key, value in counter_data.items():
            counter_proxy[key] = value

        # logs_proxy에 복원
        logs_data = snapshot.get("logs", [])
        for log_item in logs_data:
            logs_proxy.append(log_item)

        # 복원 정보 로깅
        logger.info(
            f"[ErrorLogger] Restored from snapshot v{version}: {len(counter_data)} counters, {len(logs_data)} logs"
        )
        return True

    except FileNotFoundError:
        logger.info("[ErrorLogger] No snapshot found - first run or clean start")
        return False
    except Exception as e:
        logger.warning(f"[ErrorLogger] Failed to restore snapshot: {e}")
        return False


def init_logging():
    level = config.LOG_LEVELS.get(config.ADTA_LOG_LEVELS, logging.INFO)
    logging.basicConfig(
        level=level,
        format="[%(asctime)s] %(levelname)s] %(message)s",
    )
    logging.getLogger("uvicorn.error").propagate = False
    logging.getLogger("uvicorn.access").propagate = False


def main():
    # 멀티프로세싱 시작 방식 설정
    set_start_method("fork")

    # Logging 설정
    init_logging()
    logger.info("Starting application")

    # 설정 로드
    servers, rules, mode, vdm = get_config(config.CONFIG_SENSOR_PATH)
    device_cfg = get_device_config(config.CONFIG_DEVICE_PATH)

    logging.info(f"[WORKERS] parse={config.PARSE_WORKERS}, save={config.SAVE_WORKERS}")
    for dev, setting in device_cfg.devices.items():
        logging.info(f"[DeviceConfig-450] {dev} sources: {setting.selected_61162_450}")
    for rule in rules:
        logging.info(f"[RuleConfig] {rule}")

    # Inter-process 자원
    mgr = Manager()
    error_counter = mgr.dict()
    error_logs = mgr.list()
    recv_ctr = Value("I", 0)
    parse_ctr = Value("I", 0)
    save_ctr = Value("I", 0)
    metrics = Manager().dict(
        recv_dps_10s_avg=0.0, parse_sps_10s_avg=0.0, save_sps_10s_avg=0.0, raw_backlog=0
    )
    raw_queue = Queue(maxsize=config.QUEUE_SIZE)
    log_queue = Queue(maxsize=config.QUEUE_SIZE)
    monitoring_queue = Queue(maxsize=5000)  # 패킷 모니터링용 큐 (누수 방지 포함)
    monitoring_enabled = Value("b", False)  # 모니터링 ON/OFF 플래그

    stop_event = Event()

    # 스냅샷에서 에러 데이터 복원 시도
    restored = restore_error_counter_from_snapshot(error_counter, error_logs)

    # 복원되지 않은 경우에만 기본값으로 초기화
    if not restored:
        init_error_counter(error_counter, initial_last_ts="-")

    # # CHANGED: ErrorLogger 전용 프로세스 띄우기
    errorLogger = ErrorLogger(log_queue, error_counter, error_logs)
    errorLogger_proc = Process(target=errorLogger.run, daemon=True)
    errorLogger_proc.start()

    # 5) 워커 시작 함수 (간소화)
    def spawn(target, args=()):
        p = Process(target=target, args=args, daemon=True)
        p.start()
        return p

    # 워커 프로세스 리스트
    workers = []

    # 모니터 워커
    cfg = MonitorConfig(recv_ctr, parse_ctr, save_ctr, metrics, raw_queue, log_queue, stop_event)  # type: ignore
    workers.append(spawn(monitor_worker, (cfg,)))

    # Receiver 워커
    workers.append(spawn(receive_worker, (mode, vdm, servers, raw_queue, recv_ctr)))
    for _ in range(config.PARSE_WORKERS):
        workers.append(
            spawn(
                parse_and_save_worker,
                (
                    device_cfg,
                    rules,
                    redis_mgr,
                    mode,
                    vdm,
                    raw_queue,
                    parse_ctr,
                    save_ctr,
                    log_queue,
                    monitoring_queue,  # 모니터링 큐 추가
                    monitoring_enabled,  # 모니터링 ON/OFF 플래그 추가
                ),
            )
        )

    # FastAPI 상태에 공유 리소스 연결
    app.state.redis_mgr = redis_mgr
    app.state.metrics = metrics
    app.state.stop_event = stop_event
    app.state.relay = relay
    app.state.error_counter = error_counter
    app.state.error_logs = error_logs
    app.state.redis_mgr = redis_mgr  # Redis 매니저 추가
    app.state.monitoring_queue = monitoring_queue  # 패킷 모니터링 큐 추가
    app.state.monitoring_enabled = monitoring_enabled  # 모니터링 ON/OFF 플래그 추가

    # 9) SIGTERM 핸들러: FastAPI shutdown 트리거
    def handle_sigterm(signum, frame):
        app.state.stop_event.set()
        logger.info("SIGTERM received, initiating shutdown")

    signal.signal(signal.SIGTERM, handle_sigterm)

    # 10) Uvicorn 실행 (비동기 진입)
    logger.info(f"[API] Starting FastAPI server on {config.API_HOST}:{config.API_PORT}")
    uvicorn.run(app, host=config.API_HOST, port=config.API_PORT, log_level="info")


async def on_shutdown():
    logging.info("[Shutdown] FastAPI shutdown event triggered.")
    try:
        relay.close()
        logging.info("[Shutdown] UDP relay socket closed.")
    except Exception:
        logging.warning("[Shutdown] Could not close UDP relay socket.")


app.add_event_handler("shutdown", on_shutdown)


if __name__ == "__main__":
    main()
