# ============================================
# Device Source Configuration 설명
# ============================================
# - available_ids: 해당 장비(Device)에서 선택 가능한 소스 ID 목록
# - limit: 설정 가능한 최대 소스 수
# - selected_61162_450: 실제 사용할 소스 ID들을 순서대로 나열
#   - 개수는 limit 이하여야 함
#   - 미사용 시 빈 리스트([])로 설정
# ============================================

metadata:
  last_search_time: null
  last_search_duration: null
  last_search_total_messages: null
  last_search_status: null
  last_search_updated_devices: null

devices:
  GPS:
    available_ids: [GP0001, GP0002]     # [FE-select]: 선택 가능한 소스
    available_sentence: [GGA, GLL, VTG, ZDA] # [FE-search]: 탐색 기준, engram 과 맞아야 함.     
    limit: 3
    selected_61162_450: [GP0001, GP0002]          # [output] GPS1:GPGGA , GPS2:GPGGA
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  GYRO:
    available_ids: [HE0001, HE0002]
    available_sentence: [HDT, THS, ROT]
    limit: 3
    selected_61162_450: [HE0001, HE0002]          # [output] GYRO1:HEHDT, GYRO2:HEHDT
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  ECHO_SOUNDER:
    available_ids: [SD0001, SD0002]
    available_sentence: [DPT, DBT]
    limit: 2
    selected_61162_450: [SD0001]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  ANEMOMETER:
    available_ids: [WI0001, WI0002]
    available_sentence: [MWV, MWD]
    limit: 2
    selected_61162_450: [WI0001]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  LOG:  # Doppler, Speed, Satellite 전부 포함
    available_ids: [VD0001, VD0002, VM0001, VM0002, VW0001, VW0002]
    available_sentence: [VBW, VHW]
    limit: 3
    selected_61162_450: [VD0001, VD0002]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  RUDDER_INDICATOR:
    available_ids: [II0061, II0001, II0002, RI0001, RI0002]
    available_sentence: [RSA]
    limit: 2
    selected_61162_450: [II0061]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  MASTER_CLOCK:
    available_ids: [ZA0001, ZA0002]
    available_sentence: [ZDA]
    limit: 2
    selected_61162_450: [ZA0001]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  ECDIS:
    available_ids: [EI0001, EI0002]
    available_sentence: [GGA, GLL, VTG, THS, HDT, VHW, ROT, VBW, MWV, MWD, DPT, DBT, VDR, RPM, ETL, RSA, ZDA]
    limit: 2
    selected_61162_450: [EI0001, EI0002]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  INS:
    available_ids: [IN0001, IN0002]
    available_sentence: [GGA, GLL, VTG, THS, HDT, VHW, ROT, VBW, MWV, MWD, DPT, DBT, VDR, RPM, ETL, RSA, ZDA]
    limit: 2
    selected_61162_450: [IN2000]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  RADAR:
    available_ids: [RA0001, RA0002, RA0011, RA0012]
    available_sentence: [TTM, TTD, TLB]
    limit: 2
    selected_61162_450: [RA0011, RA0012]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  AIS:
    available_ids: [AI0001, AI0002]
    available_sentence: [VDM, VDO]
    limit: 2
    selected_61162_450: [AI0001]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  VDR:
    available_ids: [VR0001]
    available_sentence: [GGA, GLL, VTG, THS, HDT, VHW, ROT, VBW, MWV, MWD, DPT, DBT, VDR, RPM, ETL, RSA, ZDA]
    limit: 2
    selected_61162_450: []
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성

  TELEGRAPH:
    available_ids: [BM0001, BM0002]
    available_sentence: [RPM, PRC]
    limit: 2
    selected_61162_450: [BM0001, BM0002]
    search_ids: []                                 # [auto-generated]: 패킷 모니터링으로 자동 생성
