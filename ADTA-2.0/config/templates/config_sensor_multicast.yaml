hinas_sfi: II0001
mode: production
rules:
  endpoint_rules: {}
  sentence_rules:
  - expire_time: 60
    sentence: ETL
  - ignore: false
    relay: ***********:6509
    sentence: ZDA
  - ignore: true
    relay: ***********:8006
    sentence: RRT
  source_rules: []
seatrial_simulator_mode:
  address: ***********
  name: vdm
  port: 6503
server:
- address: ***********
  message_protocol: 61162-450
  name: MISC
  port: 60001
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-450
  name: TGTD
  port: 60002
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-450
  name: SATD
  port: 60003
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-450
  name: NAVD
  port: 60004
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-450
  name: VDRD
  port: 60005
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-450
  name: RCOM
  port: 60006
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-450
  name: TIME
  port: 60007
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-450
  name: PROP
  port: 60008
  transport_protocol: udp
- address: ***********7
  message_protocol: 61162-450
  name: BAM1
  port: 60017
  transport_protocol: udp
- address: ***********8
  message_protocol: 61162-450
  name: BAM2
  port: 60018
  transport_protocol: udp
- address: ***********9
  message_protocol: 61162-450
  name: CAM1
  port: 60019
  transport_protocol: udp
- address: ***********0
  message_protocol: 61162-450
  name: CAM2
  port: 60020
  transport_protocol: udp
- address: ************
  message_protocol: 61162-450
  name: NETA
  port: 60056
  transport_protocol: udp