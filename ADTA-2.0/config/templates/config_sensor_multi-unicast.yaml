hinas_sfi: II0001
mode: production
rules:
  endpoint_rules: {}
  sentence_rules:
  - expire_time: 60
    sentence: ETL
  - ignore: false
    relay: ***********:6509
    sentence: ZDA
  - ignore: true
    relay: ***********:8006
    sentence: RRT
  source_rules: []
seatrial_simulator_mode:
  address: ***********
  name: vdm
  port: 6503
server:
- address: ***********
  message_protocol: 61162-1
  name: VDR
  port: 6501
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: GYRO
  port: 20001
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: GPS
  port: 20002
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: AIS
  port: 20003
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: WIND
  port: 20004
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: RUDDER
  port: 20005
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: ECHO
  port: 20006
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: SPEED_LOG
  port: 20007
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: X_BAND_RADAR
  port: 20008
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: S_BAND_RADAR
  port: 20009
  transport_protocol: udp
- address: ***********
  message_protocol: 61162-1
  name: SATELITE_LOG
  port: 20010
  transport_protocol: udp