"""
ADTA OAuth2 인증 라우터
기존 ADTA 구조와 로깅 시스템을 활용한 OAuth2 인증 API
"""

import logging
from fastapi import APIRouter, Request, HTTPException, <PERSON><PERSON>, Response
from fastapi.responses import JSONResponse, RedirectResponse
from typing import Optional
from pydantic import BaseModel
from urllib.parse import urlencode

from utils.config import AuthConfig, AUTH_MODULE_ENABLED
from utils.oauth2_service import (
    Oauth2Service,
    WrongSessionException,
    AuthModuleNotFoundException,
)


class GetOauth2SessionQueryRequest(BaseModel):
    code: str
    device_id: str


router = APIRouter(prefix="/api/oauth2", tags=["oauth2"])


@router.get("/client")
async def get_client():
    """클라이언트 정보 조회"""
    try:
        if not AUTH_MODULE_ENABLED:
            logging.info("[OAuth2 API] Auth module disabled")
            return JSONResponse(
                content={"message": "Auth module disabled", "result": {}}
            )

        result = Oauth2Service.get_client()
        logging.info("[OAuth2 API] Client info retrieved successfully")
        return JSONResponse(
            content={"message": "success to get client", "result": result}
        )

    except Exception as e:
        logging.exception(f"[OAuth2 API] Error getting client info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sso")
async def sso_entry(request: Request):
    """
    SSO 연동 엔트리 포인트.
    SSO 서버로 리다이렉트하기 위한 URL을 생성하여 302 리다이렉션 처리합니다.
    """
    try:
        query_params = {
            "redirect_uri": AuthConfig.CONTROL_LOGIN,
            "client_id": AuthConfig.CLIENT_ID,
        }
        redirect_url = f"{AuthConfig.AUTH_HOST}?{urlencode(query_params)}"

        logging.info(f"[OAuth2 API] SSO redirect to: {redirect_url}")
        return RedirectResponse(url=redirect_url, status_code=302)

    except Exception as e:
        logging.exception(f"[OAuth2 API] Error in SSO entry: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/session")
async def get_token(request: Request):
    """OAuth2 세션 생성"""
    try:
        if not AUTH_MODULE_ENABLED:
            logging.info(
                "[OAuth2 API] Auth module disabled, redirecting to control host"
            )
            return RedirectResponse(url=AuthConfig.CONTROL_HOST, status_code=302)

        query_params = dict(request.query_params)
        try:
            request_query = GetOauth2SessionQueryRequest(**query_params)
        except Exception as e:
            logging.warning(f"[OAuth2 API] Invalid query parameters: {e}")
            raise HTTPException(
                status_code=400, detail=f"Invalid query parameters: {e}"
            )

        try:
            result, session_id, expired_minutes, device_id = (
                Oauth2Service.create_session(**request_query.model_dump())
            )
        except WrongSessionException as e:
            logging.warning(f"[OAuth2 API] Session creation failed: {e}")
            raise HTTPException(status_code=401, detail=str(e))
        except AuthModuleNotFoundException as e:
            logging.error(f"[OAuth2 API] Auth service error: {e}")
            raise HTTPException(status_code=503, detail=str(e))

        response = RedirectResponse(url=AuthConfig.CONTROL_HOST, status_code=302)

        # 쿠키 설정
        cookie_max_age = 60 * int(expired_minutes)
        response.set_cookie(
            key="device_id",
            value=device_id,
            max_age=cookie_max_age,
            httponly=True,
            secure=True,
            samesite="strict",
            path="/",
        )
        response.set_cookie(
            key="session_id",
            value=session_id,
            max_age=cookie_max_age,
            httponly=True,
            secure=True,
            samesite="strict",
            path="/",
        )

        logging.info(
            f"[OAuth2 API] Session created and cookies set for device: {device_id}"
        )
        return response

    except HTTPException:
        raise
    except Exception as e:
        logging.exception(f"[OAuth2 API] Unexpected error in session creation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/session/refresh")
async def refresh_session(session_id: Optional[str] = Cookie(None)):
    """세션 갱신"""
    try:
        if not AUTH_MODULE_ENABLED:
            logging.info(
                "[OAuth2 API] Auth module disabled, redirecting to control host"
            )
            return RedirectResponse(url=AuthConfig.CONTROL_HOST, status_code=302)

        if not session_id:
            logging.warning("[OAuth2 API] Missing session_id cookie")
            raise HTTPException(status_code=400, detail="Missing session_id cookie")

        try:
            result, new_session_id, expired_minutes = Oauth2Service.refresh_session(
                session_id
            )
        except WrongSessionException as e:
            logging.warning(f"[OAuth2 API] Session refresh failed: {e}")
            raise HTTPException(status_code=401, detail=str(e))
        except AuthModuleNotFoundException as e:
            logging.error(f"[OAuth2 API] Auth service error during refresh: {e}")
            raise HTTPException(status_code=503, detail=str(e))

        response = RedirectResponse(url=AuthConfig.CONTROL_HOST, status_code=302)
        response.set_cookie(
            key="session_id",
            value=new_session_id,
            max_age=60 * int(expired_minutes),
            httponly=True,
            secure=True,
            samesite="strict",
            path="/",
        )

        logging.info(f"[OAuth2 API] Session refreshed: {new_session_id[:8]}...")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logging.exception(f"[OAuth2 API] Unexpected error in session refresh: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/me")
async def get_me(session_id: Optional[str] = Cookie(None)):
    """사용자 정보 조회"""
    try:
        if not AUTH_MODULE_ENABLED:
            logging.info("[OAuth2 API] Auth module disabled, returning dev user")
            return JSONResponse(
                content={
                    "message": "Auth module disabled",
                    "result": {"username": "dev_user"},
                }
            )

        if not session_id:
            logging.warning("[OAuth2 API] Unauthorized - missing session_id")
            return JSONResponse(status_code=401, content={"detail": "Unauthorized"})

        try:
            result = Oauth2Service.get_me(session_id)
            logging.debug(
                f"[OAuth2 API] User info retrieved for session: {session_id[:8]}..."
            )
            return JSONResponse(
                content={
                    "message": result.get("message"),
                    "result": result.get("result"),
                }
            )
        except WrongSessionException as e:
            logging.warning(f"[OAuth2 API] Invalid session: {e}")
            return JSONResponse(status_code=401, content={"detail": str(e)})
        except AuthModuleNotFoundException as e:
            logging.error(f"[OAuth2 API] Auth service error: {e}")
            return JSONResponse(status_code=503, content={"detail": str(e)})

    except Exception as e:
        logging.exception(f"[OAuth2 API] Unexpected error in get_me: {e}")
        return JSONResponse(status_code=500, content={"detail": str(e)})


@router.get("/health")
async def get_health():
    """인증 서비스 헬스 체크"""
    try:
        result = Oauth2Service.get_health()
        logging.debug("[OAuth2 API] Health check successful")
        return JSONResponse(
            content={
                "message": result.get("message", ""),
                "result": result.get("result", {}),
            }
        )
    except AuthModuleNotFoundException as e:
        logging.error(f"[OAuth2 API] Health check failed: {e}")
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        logging.exception(f"[OAuth2 API] Unexpected error in health check: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logout")
async def logout(session_id: Optional[str] = Cookie(None)):
    """로그아웃"""
    try:
        sso_logout_url = AuthConfig.SSO_LOGOUT_URL
        if not sso_logout_url:
            logging.error("[OAuth2 API] SSO logout URL not configured")
            raise HTTPException(
                status_code=500, detail="SSO logout URL is not configured."
            )

        if not session_id:
            logging.warning("[OAuth2 API] Logout attempt without session")
            return JSONResponse(status_code=401, content={"detail": "Unauthorized"})

        response = RedirectResponse(url=sso_logout_url, status_code=302)
        response.delete_cookie("session_id")
        response.delete_cookie("device_id")

        logging.info(f"[OAuth2 API] User logged out, session: {session_id[:8]}...")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logging.exception(f"[OAuth2 API] Unexpected error in logout: {e}")
        raise HTTPException(status_code=500, detail=str(e))
