#### api/routers/packet_monitor.py

import time
import asyncio
import re
import os
import shutil
import fcntl
from datetime import datetime
from typing import List, Dict, Optional
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, Field
import queue
import threading
from utils.config import CONFIG_DEVICE_PATH
from model.device import SearchMetadata, SearchStatus, DeviceSetting, AllDeviceSettings

# 파일 동시 수정 방지를 위한 전역 락
_config_file_lock = threading.RLock()  # 재진입 가능한 락

router = APIRouter(prefix="/api/network", tags=["network"])


# ─── 응답 모델 ────────────────────────────────────
class PacketMessage(BaseModel):
    sentence_name: str
    port: int
    source_id: Optional[str]
    talker_sentence: str
    protocol: str
    server_name: str


class PacketMonitorResponse(BaseModel):
    monitoring_duration: int
    total_unique_messages: int
    messages: List[PacketMessage]


class MonitorRequest(BaseModel):
    duration: int = Field(
        default=30, ge=1, le=300, description="모니터링 시간 (초), 1-300초"
    )


class UpdateSearchIdsRequest(BaseModel):
    duration: int = Field(
        default=30, ge=1, le=300, description="모니터링 시간 (초), 1-300초"
    )
    auto_update: bool = Field(
        default=True, description="자동으로 config 파일 업데이트 여부"
    )


class SearchIdsUpdateResponse(BaseModel):
    monitoring_duration: int
    total_unique_messages: int
    updated_devices: Dict[str, List[str]]  # device_name -> search_ids
    config_updated: bool
    message: str
    metadata: Optional[SearchMetadata] = None


# ─── 모니터링 데이터 수집기 ────────────────────────────────────
class PacketMonitor:
    def __init__(self):
        self.collected_messages: Dict[str, PacketMessage] = {}
        self.last_cleanup = time.time()

    async def collect_from_queue(
        self, monitoring_queue, duration: int
    ) -> List[PacketMessage]:
        """모니터링 큐에서 지정된 시간 동안 데이터 수집"""
        start_time = time.time()
        collected = {}

        while time.time() - start_time < duration:
            try:
                # 논블로킹으로 큐에서 데이터 가져오기
                monitoring_data = monitoring_queue.get_nowait()

                srv = monitoring_data["server_config"]
                parsed_data = monitoring_data["parsed_data"]

                # 메시지 정보 추출
                sentence_name = getattr(parsed_data, "sentence_name", "")
                talker = getattr(parsed_data, "talker", "")
                port = srv.port
                server_name = srv.name

                # source_id 추출 (61162-450만 해당)
                source_id = None
                protocol = "61162-1"
                if hasattr(parsed_data, "tag") and parsed_data.tag is not None:
                    source_id = getattr(parsed_data.tag, "source_id", None)
                    protocol = "61162-450"

                # talker + sentence 조합
                talker_sentence = f"{talker}{sentence_name}"

                # 고유 키 생성 (중복 제거용)
                unique_key = f"{sentence_name}|{port}|{source_id}|{talker_sentence}"

                # 메시지 객체 생성
                message = PacketMessage(
                    sentence_name=sentence_name,
                    port=port,
                    source_id=source_id,
                    talker_sentence=talker_sentence,
                    protocol=protocol,
                    server_name=server_name,
                )

                collected[unique_key] = message

            except queue.Empty:
                # 큐가 비어있으면 비동기 대기
                await asyncio.sleep(0.1)
                continue
            except Exception:
                # 파싱 오류 등은 무시하고 계속
                continue

        return list(collected.values())


# 전역 모니터 인스턴스 및 락
packet_monitor = PacketMonitor()
monitoring_lock = threading.Lock()  # 동시 모니터링 방지용 락
monitoring_lock_acquired_time = None  # 락 획득 시간 추적


def acquire_monitoring_lock_with_timeout(timeout_seconds=300):
    """
    타임아웃이 있는 모니터링 락 획득 (안전한 버전)

    Args:
        timeout_seconds: 타임아웃 시간 (기본: 5분)

    Returns:
        bool: 락 획득 성공 여부
    """
    global monitoring_lock_acquired_time

    # 기존 락이 타임아웃되었는지 확인 (더 안전한 방식)
    if monitoring_lock_acquired_time is not None:
        elapsed = time.time() - monitoring_lock_acquired_time
        if elapsed > timeout_seconds:
            print(f"[DEBUG] ⚠️  Detected stuck lock after {elapsed:.1f}s")

            # 강제 해제 대신 경고만 출력하고 새로운 시도
            # 실제 사용 중인 락을 강제로 해제하면 데이터 무결성 문제 발생 가능
            print(f"[DEBUG] Waiting for lock to be released naturally...")

            # 짧은 시간 대기 후 다시 시도
            start_wait = time.time()
            while time.time() - start_wait < 30:  # 30초 추가 대기
                if monitoring_lock.acquire(blocking=False):
                    monitoring_lock_acquired_time = time.time()
                    print(f"[DEBUG] ✅ Lock acquired after waiting")
                    return True
                time.sleep(0.5)

            # 30초 대기 후에도 안 되면 포기
            print(f"[DEBUG] ❌ Could not acquire lock after timeout + 30s wait")
            return False

    # 일반적인 락 획득 시도
    if monitoring_lock.acquire(blocking=False):
        monitoring_lock_acquired_time = time.time()
        return True

    return False


def release_monitoring_lock():
    """안전한 모니터링 락 해제"""
    global monitoring_lock_acquired_time

    try:
        monitoring_lock.release()
        monitoring_lock_acquired_time = None
    except:
        pass  # 이미 해제된 경우


def cleanup_old_backups(file_path, max_backups=5):
    """
    오래된 백업 파일들 정리 (무한 누적 방지)

    Args:
        file_path: 원본 파일 경로
        max_backups: 보존할 최대 백업 개수
    """
    try:
        backup_dir = os.path.dirname(file_path)
        backup_name = os.path.basename(file_path) + ".backup."

        # 백업 파일들 찾기
        backup_files = []
        for filename in os.listdir(backup_dir):
            if filename.startswith(backup_name):
                full_path = os.path.join(backup_dir, filename)
                backup_files.append((full_path, os.path.getmtime(full_path)))

        # 수정 시간 기준으로 정렬 (최신 순)
        backup_files.sort(key=lambda x: x[1], reverse=True)

        # 오래된 백업 파일들 삭제
        for backup_path, _ in backup_files[max_backups:]:
            try:
                os.remove(backup_path)
                print(f"[DEBUG] Removed old backup: {backup_path}")
            except:
                pass  # 삭제 실패해도 계속 진행

    except Exception as e:
        print(f"[DEBUG] Backup cleanup failed: {e}")


def safe_file_write(file_path, content):
    """
    안전한 파일 쓰기 (백업 + 원자적 쓰기 + 동시성 안전)

    Args:
        file_path: 대상 파일 경로
        content: 쓸 내용

    Raises:
        Exception: 파일 쓰기 실패 시
    """
    # 동시성 안전을 위한 락 획득
    with _config_file_lock:
        backup_path = f"{file_path}.backup"
        temp_path = f"{file_path}.tmp"

        try:
            # 1. 기존 파일 백업 (존재하는 경우)
            if os.path.exists(file_path):
                shutil.copy2(file_path, backup_path)

            # 2. 임시 파일에 내용 쓰기 (파일 락 적용)
            with open(temp_path, "w", encoding="utf-8") as f:
                # Unix 계열에서 파일 락 적용 (동시 쓰기 방지)
                try:
                    fcntl.flock(f.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                except (OSError, AttributeError):
                    # Windows나 락 실패 시 무시하고 계속 진행
                    pass
                f.write(content)

            # 3. 원자적 이동 (rename은 원자적 연산)
            os.rename(temp_path, file_path)

            # 4. 성공 시 백업 파일 관리 (무한 누적 방지)
            if os.path.exists(backup_path):
                try:
                    # 기존 백업 파일들 정리 (최대 5개만 보존)
                    cleanup_old_backups(file_path, max_backups=5)

                    # 새 백업 파일 생성
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    archived_backup = f"{backup_path}.{timestamp}"
                    os.rename(backup_path, archived_backup)
                    print(f"[DEBUG] Backup archived: {archived_backup}")
                except:
                    os.remove(backup_path)  # 실패 시 기존 방식

        except Exception as e:
            # 5. 실패 시 복원 시도
            try:
                if os.path.exists(backup_path):
                    shutil.copy2(backup_path, file_path)
                    print(f"[DEBUG] File restored from backup due to error: {e}")
            except:
                pass

            # 6. 임시 파일 정리
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except:
                pass

            raise e


# ─── Search IDs 생성 로직 ────────────────────────────────────
def extract_prefixes_from_available_ids(available_ids: List[str]) -> List[str]:
    """available_ids에서 prefix들을 추출 (앞 2자리)"""
    prefixes = []
    for source_id in available_ids:
        if len(source_id) >= 2:
            prefix = source_id[:2]
            if prefix not in prefixes:
                prefixes.append(prefix)
    return sorted(prefixes)


def generate_search_ids_for_device(
    available_ids: List[str], discovered_source_ids: List[str]
) -> List[str]:
    """디바이스별로 search_ids 생성"""
    if not available_ids:
        return []

    # 1. available_ids에서 prefix들 추출
    allowed_prefixes = extract_prefixes_from_available_ids(available_ids)

    # 2. 발견된 source_id들 중에서 허용된 prefix와 매칭되는 것들만 필터링
    search_ids = []
    for source_id in discovered_source_ids:
        if len(source_id) >= 2:
            prefix = source_id[:2]
            if prefix in allowed_prefixes:
                search_ids.append(source_id)

    # 3. 중복 제거 및 정렬
    return sorted(list(set(search_ids)))


def analyze_packets_for_search_ids(
    messages: List[PacketMessage],
) -> Dict[str, List[str]]:
    """패킷 메시지들을 분석하여 디바이스별 search_ids 생성"""
    from utils.config_parser import load_yaml_config

    try:
        # config_source_device.yaml 로드
        device_config = load_yaml_config(CONFIG_DEVICE_PATH)
        devices_config = device_config.get("devices", {})

        # 발견된 source_id들 수집
        discovered_source_ids = []
        for msg in messages:
            if msg.source_id:
                discovered_source_ids.append(msg.source_id)

        # 중복 제거
        discovered_source_ids = list(set(discovered_source_ids))

        # 각 디바이스별로 search_ids 생성 (모든 디바이스 포함!)
        updated_devices = {}
        for device_name, device_settings in devices_config.items():
            available_ids = device_settings.get("available_ids", [])
            if available_ids:  # available_ids가 있는 디바이스만 처리
                search_ids = generate_search_ids_for_device(
                    available_ids, discovered_source_ids
                )
            else:
                search_ids = []  # available_ids가 없으면 빈 리스트

            # 모든 디바이스를 updated_devices에 포함 (빈 리스트라도!)
            updated_devices[device_name] = search_ids

        return updated_devices

    except Exception as e:
        raise HTTPException(500, f"Failed to analyze packets for search_ids: {e}")


# ─── 메타데이터 관리 함수 ────────────────────────────────────
def get_current_metadata() -> Optional[SearchMetadata]:
    """현재 config 파일에서 메타데이터 읽기"""
    try:
        with open(CONFIG_DEVICE_PATH, "r", encoding="utf-8") as f:
            content = f.read()

        # metadata 섹션 찾기
        metadata_pattern = r"metadata:\s*\n((?:\s+\w+:.*\n)*)"
        match = re.search(metadata_pattern, content)

        if not match:
            return None

        metadata_section = match.group(1)
        metadata_dict = {}

        # 각 메타데이터 필드 파싱
        for line in metadata_section.split("\n"):
            if ":" in line:
                key, value = line.split(":", 1)
                key = key.strip()
                value = value.strip().strip('"')

                if (
                    key == "last_search_duration"
                    or key == "last_search_total_messages"
                    or key == "last_search_updated_devices"
                ):
                    metadata_dict[key] = int(value) if value.isdigit() else None
                else:
                    metadata_dict[key] = value if value else None

        return SearchMetadata(**metadata_dict)

    except Exception as e:
        print(f"[DEBUG] Error reading metadata: {e}")
        return None


def create_new_metadata(
    duration: int,
    total_messages: int,
    updated_devices_count: int,
    status: SearchStatus = SearchStatus.SUCCESS,
) -> SearchMetadata:
    """새로운 메타데이터 생성 (타입 안전)"""
    return SearchMetadata(
        last_search_time=datetime.now().isoformat(),
        last_search_duration=duration,
        last_search_total_messages=total_messages,
        last_search_status=status,
        last_search_updated_devices=updated_devices_count,
    )


def update_config_with_search_ids_and_metadata(
    updated_devices: Dict[str, List[str]], new_metadata: SearchMetadata
) -> bool:
    """config_source_device.yaml 파일에 search_ids와 metadata 업데이트 (YAML 라이브러리 사용)"""
    try:
        print(f"[DEBUG] Updating search_ids for devices: {updated_devices}")
        print(f"[DEBUG] Updating metadata: {new_metadata}")

        # 1. 기존 설정 로드 (YAML 라이브러리 사용, 안전한 방식)
        from utils.config_parser import load_yaml_config

        try:
            existing_config = load_yaml_config(CONFIG_DEVICE_PATH)
        except Exception as e:
            print(f"[DEBUG] ⚠️  YAML 파일 로드 실패, 기본 구조로 초기화: {e}")
            # 기본 구조로 초기화
            existing_config = {"metadata": {}, "devices": {}}

        updated_count = 0
        metadata_updated = False

        # 2. 메타데이터 업데이트 (YAML 라이브러리 방식)
        # Enum 값을 문자열로 변환
        status_value = (
            new_metadata.last_search_status.value
            if new_metadata.last_search_status
            else None
        )

        # 메타데이터 딕셔너리 생성
        metadata_dict = {
            "last_search_time": new_metadata.last_search_time,
            "last_search_duration": new_metadata.last_search_duration,
            "last_search_total_messages": new_metadata.last_search_total_messages,
            "last_search_status": status_value,
            "last_search_updated_devices": new_metadata.last_search_updated_devices,
        }

        # 기존 config에 metadata 업데이트
        existing_config["metadata"] = metadata_dict
        metadata_updated = True
        print("[DEBUG] Updated metadata using YAML library")

        # 3. search_ids 업데이트 (YAML 라이브러리 방식)
        devices_config = existing_config.get("devices", {})

        for device_name, search_ids in updated_devices.items():
            if device_name in devices_config:
                # 기존 디바이스 설정에 search_ids 업데이트
                devices_config[device_name]["search_ids"] = search_ids
                updated_count += 1
                print(f"[DEBUG] Updated {device_name}: search_ids = {search_ids}")
            else:
                print(f"[DEBUG] ⚠️  Device {device_name} not found in config")

        print(f"[DEBUG] Total devices updated: {updated_count}")

        # 4. YAML 파일 저장 (Device API와 동일한 방식)
        if updated_count > 0 or metadata_updated:
            # Device API와 동일한 save_yaml 방식 사용
            import yaml

            yaml_content = yaml.safe_dump(
                existing_config,
                default_flow_style=None,  # 짧은 배열은 자동으로 인라인
                allow_unicode=True,
                sort_keys=False,
                width=1000,  # 긴 줄도 허용하여 배열이 인라인으로 유지되도록
            )

            with open(CONFIG_DEVICE_PATH, "w", encoding="utf-8") as f:
                f.write(yaml_content)

            print(f"[DEBUG] Config file saved successfully (YAML library format)")
            print(
                f"[DEBUG] Devices updated: {updated_count}, Metadata updated: {metadata_updated}"
            )
            return True
        else:
            print(f"[DEBUG] ⚠️  No changes detected, file not updated")
            return False

    except Exception as e:
        print(f"[DEBUG] Error updating config: {e}")
        raise HTTPException(500, f"Failed to update config file: {e}")


# ─── API 엔드포인트 ────────────────────────────────────
@router.post("/monitor", response_model=PacketMonitorResponse)
async def monitor_packets(request: MonitorRequest, req: Request):
    """
    실시간 패킷 모니터링

    지정된 시간 동안 시스템에서 수신되는 패킷을 모니터링하고,
    sentence_name, 포트, source_id, talker+sentence 정보를 반환합니다.

    Args:
        request: 모니터링 요청 (duration)

    Returns:
        PacketMonitorResponse: 수집된 패킷 정보들
    """
    # 동시 모니터링 방지 (논블로킹 체크)
    if not monitoring_lock.acquire(blocking=False):
        raise HTTPException(
            409,
            "Another monitoring session is already running. Please wait or stop the current session.",
        )

    try:
        # FastAPI state에서 모니터링 관련 객체들 가져오기
        monitoring_queue = req.app.state.monitoring_queue
        monitoring_enabled = req.app.state.monitoring_enabled

        # 1. 기존 큐 비우기
        cleared_count = 0
        while not monitoring_queue.empty():
            try:
                monitoring_queue.get_nowait()
                cleared_count += 1
            except queue.Empty:
                break

        # 2. 모니터링 활성화
        with monitoring_enabled.get_lock():
            monitoring_enabled.value = True

        try:
            # 3. 지정된 시간 동안 패킷 수집
            messages = await packet_monitor.collect_from_queue(
                monitoring_queue, request.duration
            )

            # 4. 결과 정렬 (sentence_name, port 순)
            messages.sort(key=lambda x: (x.sentence_name, x.port, x.source_id or ""))

            return PacketMonitorResponse(
                monitoring_duration=request.duration,
                total_unique_messages=len(messages),
                messages=messages,
            )
        finally:
            # 5. 모니터링 비활성화 및 큐 정리
            with monitoring_enabled.get_lock():
                monitoring_enabled.value = False

            # 남은 데이터 정리
            while not monitoring_queue.empty():
                try:
                    monitoring_queue.get_nowait()
                except queue.Empty:
                    break

    except Exception as e:
        # 에러 발생 시에도 모니터링 비활성화
        try:
            with monitoring_enabled.get_lock():
                monitoring_enabled.value = False
        except:
            pass
        raise HTTPException(500, f"Packet monitoring failed: {str(e)}")
    finally:
        # 락 해제
        monitoring_lock.release()


@router.get("/monitor/status")
async def get_monitor_status(req: Request):
    """
    모니터링 큐 상태 조회

    Returns:
        dict: 모니터링 큐의 현재 상태 정보
    """
    try:
        monitoring_queue = req.app.state.monitoring_queue
        monitoring_enabled = req.app.state.monitoring_enabled

        queue_size = monitoring_queue.qsize()
        is_enabled = monitoring_enabled.value

        # 락 상태 확인
        lock_acquired = monitoring_lock.locked()

        return {
            "monitoring_enabled": is_enabled,
            "queue_size": queue_size,
            "queue_maxsize": monitoring_queue._maxsize,
            "is_full": monitoring_queue.full(),
            "is_empty": monitoring_queue.empty(),
            "status": "ACTIVE" if is_enabled else "INACTIVE",
            "session_locked": lock_acquired,
            "concurrent_access": "BLOCKED" if lock_acquired else "AVAILABLE",
        }

    except Exception as e:
        raise HTTPException(500, f"Failed to get monitor status: {str(e)}")


@router.delete("/monitor/clear")
async def clear_monitor_queue(req: Request):
    """
    모니터링 큐 비우기

    Returns:
        dict: 정리된 메시지 수
    """
    try:
        monitoring_queue = req.app.state.monitoring_queue
        cleared_count = 0

        # 큐의 모든 데이터 제거
        while not monitoring_queue.empty():
            try:
                monitoring_queue.get_nowait()
                cleared_count += 1
            except queue.Empty:
                break

        return {"message": "Monitor queue cleared", "cleared_messages": cleared_count}

    except Exception as e:
        raise HTTPException(500, f"Failed to clear monitor queue: {str(e)}")


@router.post("/monitor/start")
async def start_monitoring(req: Request):
    """
    모니터링 수동 시작

    Returns:
        dict: 모니터링 시작 결과
    """
    try:
        monitoring_enabled = req.app.state.monitoring_enabled

        # 현재 상태 확인
        with monitoring_enabled.get_lock():
            was_enabled = monitoring_enabled.value
            if was_enabled:
                return {
                    "message": "Monitoring is already running",
                    "was_enabled": True,
                    "now_enabled": True,
                    "warning": None,
                }
            monitoring_enabled.value = True

        return {
            "message": "Monitoring started",
            "was_enabled": was_enabled,
            "now_enabled": True,
            "warning": (
                "Remember to call /monitor/stop to prevent memory issues"
                if not was_enabled
                else None
            ),
        }

    except Exception as e:
        raise HTTPException(500, f"Failed to start monitoring: {str(e)}")


@router.post("/monitor/stop")
async def stop_monitoring(req: Request):
    """
    모니터링 수동 중지 및 큐 정리

    Returns:
        dict: 모니터링 중지 결과
    """
    try:
        monitoring_queue = req.app.state.monitoring_queue
        monitoring_enabled = req.app.state.monitoring_enabled

        # 모니터링 비활성화
        with monitoring_enabled.get_lock():
            was_enabled = monitoring_enabled.value
            monitoring_enabled.value = False

        # 큐 정리
        cleared_count = 0
        while not monitoring_queue.empty():
            try:
                monitoring_queue.get_nowait()
                cleared_count += 1
            except queue.Empty:
                break

        return {
            "message": "Monitoring stopped and queue cleared",
            "was_enabled": was_enabled,
            "now_enabled": False,
            "cleared_messages": cleared_count,
        }

    except Exception as e:
        raise HTTPException(500, f"Failed to stop monitoring: {str(e)}")


@router.post("/monitor/update-search-ids", response_model=SearchIdsUpdateResponse)
async def update_search_ids(request: UpdateSearchIdsRequest, req: Request):
    """
    패킷 모니터링을 통한 search_ids 자동 업데이트

    지정된 시간 동안 패킷을 모니터링하고, 발견된 source_id들을 분석하여
    config_source_device.yaml의 search_ids 필드를 자동으로 업데이트합니다.

    Args:
        request: 업데이트 요청 (duration, auto_update)

    Returns:
        SearchIdsUpdateResponse: 업데이트 결과 및 생성된 search_ids 정보
    """
    # 동시 모니터링 방지 (타임아웃 기반 락)
    if not acquire_monitoring_lock_with_timeout():
        raise HTTPException(
            409,
            "Another monitoring session is already running. Please wait or stop the current session.",
        )

    try:
        # FastAPI state에서 모니터링 관련 객체들 가져오기
        monitoring_queue = req.app.state.monitoring_queue
        monitoring_enabled = req.app.state.monitoring_enabled

        # 1. 기존 큐 비우기
        cleared_count = 0
        while not monitoring_queue.empty():
            try:
                monitoring_queue.get_nowait()
                cleared_count += 1
            except queue.Empty:
                break

        # 2. 모니터링 활성화
        with monitoring_enabled.get_lock():
            monitoring_enabled.value = True

        try:
            # 3. 지정된 시간 동안 패킷 수집
            messages = await packet_monitor.collect_from_queue(
                monitoring_queue, request.duration
            )

            # 4. 패킷 분석하여 search_ids 생성
            updated_devices = analyze_packets_for_search_ids(messages)

            # 5. 이전 메타데이터 조회
            previous_metadata = get_current_metadata()

            # 6. config 파일 업데이트 (auto_update가 True인 경우)
            config_updated = False
            if request.auto_update:
                # 새로운 메타데이터 생성
                new_metadata = create_new_metadata(
                    duration=request.duration,
                    total_messages=len(messages),
                    updated_devices_count=len(updated_devices),
                    status=SearchStatus.SUCCESS if messages else SearchStatus.NO_DATA,
                )
                config_updated = update_config_with_search_ids_and_metadata(
                    updated_devices, new_metadata
                )

            # 7. 응답 생성
            message = "Search IDs analysis completed"
            if request.auto_update:
                if config_updated:
                    message += " and config file updated"
                    # 실제로 발견된 source_id가 있는지 확인
                    total_found = sum(len(ids) for ids in updated_devices.values())
                    if total_found > 0:
                        message += f" ({total_found} source IDs found)"
                    else:
                        message += " (no source IDs found - empty lists set)"
                else:
                    message += " but no changes detected (config file not updated)"
            else:
                message += " (config file not updated - auto_update=False)"

            # 8. 메타데이터 응답 생성
            response_metadata = None
            if config_updated:
                # 새로 업데이트된 메타데이터 반환
                response_metadata = SearchMetadata(
                    last_search_time=datetime.now().isoformat(),
                    last_search_duration=request.duration,
                    last_search_total_messages=len(messages),
                    last_search_status=(
                        SearchStatus.SUCCESS if messages else SearchStatus.NO_DATA
                    ),
                    last_search_updated_devices=len(updated_devices),
                )
            elif previous_metadata:
                # 기존 메타데이터 반환
                response_metadata = previous_metadata

            return SearchIdsUpdateResponse(
                monitoring_duration=request.duration,
                total_unique_messages=len(messages),
                updated_devices=updated_devices,
                config_updated=config_updated,
                message=message,
                metadata=response_metadata,
            )

        finally:
            # 7. 모니터링 비활성화 및 큐 정리
            with monitoring_enabled.get_lock():
                monitoring_enabled.value = False

            # 남은 데이터 정리
            while not monitoring_queue.empty():
                try:
                    monitoring_queue.get_nowait()
                except queue.Empty:
                    break

    except HTTPException:
        raise
    except Exception as e:
        # 에러 발생 시에도 모니터링 비활성화
        try:
            with monitoring_enabled.get_lock():
                monitoring_enabled.value = False
        except:
            pass
        raise HTTPException(500, f"Search IDs update failed: {str(e)}")
    finally:
        # 락 해제
        release_monitoring_lock()
