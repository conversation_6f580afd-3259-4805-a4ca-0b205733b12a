from pipeline.parser import init_error_counter
from utils.config import ERROR_COUNTER_MAX

from fastapi import APIRouter, Request, status
from typing import Dict, List, Any
from pydantic import BaseModel
from datetime import datetime, timezone

router = APIRouter(prefix="/api/errors", tags=["errors"])


class CountResponse(BaseModel):
    counts: Dict[str, Any]


class LogItem(BaseModel):
    timestamp: str
    msg: str


@router.get("/count", response_model=CountResponse)
async def get_error_counts(request: Request):
    raw = request.app.state.error_counter
    out: Dict[str, Any] = {}
    for k, v in raw.items():
        if k.endswith("__last_timestamp"):
            # v 가 "-" 면 그대로, 아니면 strftime
            if isinstance(v, str):
                out[k] = v
            else:
                dt = datetime.fromtimestamp(int(v), tz=timezone.utc)
                out[k] = dt.strftime("%Y-%m-%dT%H:%M:%S")
        else:
            count_value = int(v)
            # 최대값에 도달한 경우 "+" 표시 추가 (Frontend 인지용)
            if count_value >= ERROR_COUNTER_MAX:
                out[k] = f"{count_value}+"
                out[f"{k}__is_maxed"] = True  # Frontend에서 감지할 수 있는 플래그
            else:
                out[k] = count_value
                out[f"{k}__is_maxed"] = False
    return {"counts": out}


@router.get("/logs", response_model=List[LogItem])
async def get_error_logs(request: Request):
    """
    최근 N개의 디버그 로그(timestamp: "YYYY-MM-DDTHH:MM:SS", msg: str) 반환
    """
    logs = request.app.state.error_logs  # Manager.list proxy
    if not logs:
        return [{"timestamp": "-", "msg": ""}]  # CHANGED: 빈 리스트 대신 placeholder

    out = []
    for timestamp, msg in logs:
        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        out.append(
            {"timestamp": dt.strftime("%Y-%m-%dT%H:%M:%S"), "msg": msg}
        )  # CHANGED: 포맷 적용
    return out


@router.post("/reset", status_code=status.HTTP_204_NO_CONTENT)
async def reset_errors(request: Request):
    """
    error_counter, error_logs 모두 초기화
    """
    counter = request.app.state.error_counter
    logs = request.app.state.error_logs

    counter.clear()
    init_error_counter(counter, initial_last_ts="-")

    logs[:] = []
    return
