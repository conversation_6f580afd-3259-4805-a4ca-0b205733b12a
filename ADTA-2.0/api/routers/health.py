from fastapi import APIRouter, Request, HTTPException
from datetime import datetime, timezone
from typing import Dict, Any
import time

router = APIRouter()

# 서비스 시작 시간 기록
_service_start_time = time.time()


def get_safe_uptime() -> Dict[str, Any]:
    """안전한 uptime 계산 (오버플로우 방지)"""
    uptime_seconds = int(time.time() - _service_start_time)

    # 매우 큰 값 제한 (약 68년)
    if uptime_seconds > 2147483647:  # 32bit int 최대값
        return {
            "uptime_seconds": 2147483647,
            "uptime_human": "68+ years",
            "note": "uptime_capped",
        }

    # 사람이 읽기 쉬운 형태로 변환
    days = uptime_seconds // 86400
    hours = (uptime_seconds % 86400) // 3600
    minutes = (uptime_seconds % 3600) // 60
    seconds = uptime_seconds % 60

    if days > 0:
        human_readable = f"{days}d {hours}h {minutes}m"
    elif hours > 0:
        human_readable = f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        human_readable = f"{minutes}m {seconds}s"
    else:
        human_readable = f"{seconds}s"

    return {"uptime_seconds": uptime_seconds, "uptime_human": human_readable}


def check_redis_status(request: Request) -> Dict[str, Any]:
    """Redis 연결 상태 확인"""
    try:
        redis_mgr = request.app.state.redis_mgr
        if redis_mgr.is_connected:
            return {"status": "healthy", "description": "Redis connection stable"}
        else:
            return {"status": "unhealthy", "description": "Redis connection failed"}
    except Exception as e:
        return {"status": "unhealthy", "description": f"Redis check failed: {str(e)}"}


def check_workers_status(request: Request) -> Dict[str, Any]:
    """워커들 상태 확인 (메트릭 기반)"""
    try:
        metrics = dict(request.app.state.metrics)

        recv_rate = metrics.get("recv_dps_10s_avg", 0)
        parse_rate = metrics.get("parse_sps_10s_avg", 0)
        save_rate = metrics.get("save_sps_10s_avg", 0)

        # 워커 활동 여부 판단
        if parse_rate >= 0 and save_rate >= 0:  # 음수가 아니면 정상
            return {
                "status": "healthy",
                "description": f"All workers active (parse: {parse_rate:.1f}/s, save: {save_rate:.1f}/s)",
            }
        else:
            return {
                "status": "unhealthy",
                "description": "Workers not responding - no metric updates",
            }
    except Exception as e:
        return {"status": "unhealthy", "description": f"Worker check failed: {str(e)}"}


def check_queues_status(request: Request) -> Dict[str, Any]:
    """큐 상태 확인"""
    try:
        metrics = dict(request.app.state.metrics)

        raw_backlog = metrics.get("raw_backlog", 0)
        error_queue_size = metrics.get("error_log_queue", 0)

        if raw_backlog < 1000:
            return {
                "status": "healthy",
                "description": f"Queue backlog normal ({raw_backlog}/20000)",
            }
        elif raw_backlog < 10000:
            return {
                "status": "degraded",
                "description": f"Queue backlog increasing ({raw_backlog}/20000)",
            }
        else:
            return {
                "status": "unhealthy",
                "description": f"Queue backlog critical ({raw_backlog}/20000)",
            }
    except Exception as e:
        return {"status": "unhealthy", "description": f"Queue check failed: {str(e)}"}


def determine_overall_status(components: Dict[str, Dict[str, Any]]) -> tuple[str, str]:
    """전체 상태 결정"""
    statuses = [comp["status"] for comp in components.values()]

    if "unhealthy" in statuses:
        unhealthy_components = [
            name for name, comp in components.items() if comp["status"] == "unhealthy"
        ]
        return "unhealthy", f"Critical failure in: {', '.join(unhealthy_components)}"
    elif "degraded" in statuses:
        degraded_components = [
            name for name, comp in components.items() if comp["status"] == "degraded"
        ]
        return "degraded", f"Performance issues in: {', '.join(degraded_components)}"
    else:
        return "healthy", "All components operating normally"


@router.get("/live")
async def liveness():
    """
    K8s Liveness Probe - API 서버 생존 확인

    FastAPI 프로세스가 살아있고 HTTP 요청에 응답할 수 있는지만 확인합니다.
    실패 시 K8s가 Pod를 재시작합니다.
    """
    return {"status": "healthy", "description": "API server is alive"}


@router.get("/ready")
async def readiness(request: Request):
    """
    K8s Readiness Probe - 서비스 준비 상태 확인

    워커들, Redis, 큐 등 전체 서비스가 트래픽을 처리할 준비가 되었는지 확인합니다.
    실패 시 K8s가 트래픽을 차단합니다 (Pod는 유지).
    """
    try:
        # 각 컴포넌트 상태 확인
        components = {
            "redis": check_redis_status(request),
            "workers": check_workers_status(request),
            "queues": check_queues_status(request),
        }

        # 전체 상태 결정
        overall_status, description = determine_overall_status(components)

        response = {
            "status": overall_status,
            "description": description,
            "components": components,
        }

        # unhealthy면 503 (트래픽 차단), 나머지는 200
        if overall_status == "unhealthy":
            raise HTTPException(503, response)

        return response

    except HTTPException:
        raise
    except Exception as e:
        # 예상치 못한 에러 시 unhealthy 처리
        error_response = {
            "status": "unhealthy",
            "description": f"Readiness check failed: {str(e)}",
            "components": {},
        }
        raise HTTPException(503, error_response)


@router.get("/")
async def health(request: Request):
    """
    상세 헬스체크 - 모니터링 및 대시보드용

    전체 시스템의 상세한 상태 정보를 제공합니다.
    """
    try:
        # 각 컴포넌트 상태 확인
        components = {
            "api": {
                "status": "healthy",
                "description": "FastAPI server responding normally",
            },
            "redis": check_redis_status(request),
            "workers": check_workers_status(request),
            "queues": check_queues_status(request),
        }

        # 전체 상태 결정
        overall_status, description = determine_overall_status(components)

        # 메트릭 정보
        metrics = dict(request.app.state.metrics)
        uptime_info = get_safe_uptime()

        response = {
            "status": overall_status,
            "description": description,
            "issued_time": datetime.utcnow().isoformat() + "Z",
            "components": components,
            "summary": {
                **uptime_info,  # uptime_seconds, uptime_human 포함
                "queue_backlog": metrics.get("raw_backlog", 0),
                "error_queue_size": metrics.get("error_log_queue", 0),
            },
            "performance": {
                "receive_rate": metrics.get("recv_dps_10s_avg", 0),
                "parse_rate": metrics.get("parse_sps_10s_avg", 0),
                "save_rate": metrics.get("save_sps_10s_avg", 0),
                "set_rate": metrics.get("set_sps_10s_avg", 0),
                "publish_rate": metrics.get("publish_sps_10s_avg", 0),
            },
        }

        # 문제가 있는 경우 이슈 목록 추가
        if overall_status != "healthy":
            issues = []
            for name, comp in components.items():
                if comp["status"] in ["degraded", "unhealthy"]:
                    issues.append(f"{name}: {comp['description']}")
            response["issues"] = issues

        return response

    except Exception as e:
        return {
            "status": "unhealthy",
            "description": f"Health check failed: {str(e)}",
            "issued_time": datetime.utcnow().isoformat() + "Z",
            "components": {},
            "issues": ["Health check system error"],
        }


@router.get("/metrics")
async def metrics(request: Request):
    """
    상세 메트릭 정보 - 모니터링 대시보드용

    시스템 성능 지표와 상세한 워커 정보를 제공합니다.
    """
    try:
        metrics = dict(request.app.state.metrics)
        uptime_info = get_safe_uptime()

        # 큐 상태 상세 정보
        raw_backlog = metrics.get("raw_backlog", 0)
        error_queue_size = metrics.get("error_log_queue", 0)

        return {
            "issued_time": datetime.utcnow().isoformat() + "Z",
            **uptime_info,  # uptime_seconds, uptime_human 포함
            "performance": {
                "receive_rate": metrics.get("recv_dps_10s_avg", 0),
                "parse_rate": metrics.get("parse_sps_10s_avg", 0),
                "save_rate": metrics.get("save_sps_10s_avg", 0),
                "set_rate": metrics.get("set_sps_10s_avg", 0),
                "publish_rate": metrics.get("publish_sps_10s_avg", 0),
                "unit": "per_second_10s_avg",
            },
            "queues": {
                "raw_queue": {
                    "size": min(raw_backlog, 999999999),  # 10억 제한
                    "max_size": 20000,
                    "usage_percent": min(
                        round((raw_backlog / 20000) * 100, 2), 999999.99
                    ),  # 백만% 제한
                },
                "error_queue": {
                    "size": min(error_queue_size, 999999999),  # 10억 제한
                    "max_size": 20000,
                    "usage_percent": min(
                        (
                            round((error_queue_size / 20000) * 100, 2)
                            if error_queue_size > 0
                            else 0.0
                        ),
                        999999.99,  # 백만% 제한
                    ),
                },
                "monitoring_queue": {
                    "size": (
                        request.app.state.monitoring_queue.qsize()
                        if hasattr(request.app.state, "monitoring_queue")
                        else 0
                    ),
                    "max_size": 5000,
                    "enabled": (
                        request.app.state.monitoring_enabled.value
                        if hasattr(request.app.state, "monitoring_enabled")
                        else False
                    ),
                },
            },
            "workers": {
                "monitor_worker": (
                    "active" if metrics.get("recv_dps_10s_avg", 0) >= 0 else "inactive"
                ),
                "receiver_worker": (
                    "active" if metrics.get("recv_dps_10s_avg", 0) > 0 else "idle"
                ),
                "parser_workers": {
                    "count": 8,  # config.PARSE_WORKERS와 동기화 필요
                    "status": (
                        "active"
                        if metrics.get("parse_sps_10s_avg", 0) >= 0
                        else "inactive"
                    ),
                },
                "error_logger": "active",
            },
            "system": {
                "redis_connected": request.app.state.redis_mgr.is_connected,
                "total_errors": (
                    len(request.app.state.error_logs)
                    if hasattr(request.app.state, "error_logs")
                    else 0
                ),
            },
        }

    except Exception as e:
        return {
            "issued_time": datetime.utcnow().isoformat() + "Z",
            "error": f"Metrics collection failed: {str(e)}",
            "performance": {},
            "queues": {},
            "workers": {},
            "system": {},
        }
