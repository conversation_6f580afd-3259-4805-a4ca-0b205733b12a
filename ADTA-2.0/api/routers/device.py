#### api/routers/device.py

import yaml
from fastapi import APIRouter, HTTPException, Path, Request
from typing import Dict, List
from utils.config import CONFIG_DEVICE_PATH
from utils.config_parser import load_yaml_config
from model.device import DeviceSetting, AllDeviceSettings
from pydantic import BaseModel

router = APIRouter(prefix="/api/devices", tags=["devices"])


# ─── 디바이스별 센서 상태 응답 모델 ────────────────────────────────────
class DeviceSensorStatus(BaseModel):
    name: str  # "GPS1 (GP0001)" 또는 "GPS2 (NONE)"
    sentences: List[str]  # ["GPGGA", "GPGLL", "INGGA"] - 알파벳 순 정렬
    status: bool  # sentences가 1개 이상이면 True, 아니면 False


class DeviceSensorStatusResponse(BaseModel):
    sensors: List[DeviceSensorStatus]


# ─── PUT 요청용 모델 (metadata 제외) ────────────────────────────────────
class DeviceSettingsUpdateRequest(BaseModel):
    """사용자가 수정 가능한 디바이스 설정만 포함 (metadata 제외)"""

    devices: Dict[str, DeviceSetting]


def save_yaml(path: str, cfg: dict):
    """YAML 파일 저장 (간단한 인라인 배열 포맷)"""
    try:
        # 1. 기본 YAML 덤프 (flow_style=None으로 자동 결정)
        yaml_content = yaml.safe_dump(
            cfg,
            default_flow_style=None,  # 짧은 배열은 자동으로 인라인
            allow_unicode=True,
            sort_keys=False,
            width=1000,  # 긴 줄도 허용하여 배열이 인라인으로 유지되도록
        )

        # 2. 파일에 저장
        with open(path, "w", encoding="utf-8") as f:
            f.write(yaml_content)

    except Exception as e:
        raise HTTPException(500, f"Cannot write device config: {e}")


def get_device_sensor_status_data(redis_mgr) -> DeviceSensorStatusResponse:
    """
    config_source_device.yaml 기반으로 디바이스별 센서 상태를 조회하여 반환

    요구사항:
    1. Device마다 최소 2개, 최대 3개까지 출력 (선택된 개수에 따라)
    2. GPS1 (GP0001), GPS2 (GP0002) 형태로 표시
    3. 선택되지 않은 경우 GPS2 (NONE) 표시
    4. Receive Sensors는 Redis에서 실제 수신된 센텐스들 (알파벳 순)
    5. STATUS는 sentences가 1개 이상이면 true, 아니면 false
    """
    try:
        # 1. 디바이스 설정 로드
        device_config = load_yaml_config(CONFIG_DEVICE_PATH)
        devices_config = device_config.get("devices", {})

        # 2. Redis 연결 확인
        if not redis_mgr.is_connected:
            raise HTTPException(500, "Redis connection failed")

        sensors_status = []

        # 3. 각 디바이스별로 상태 조회
        for device_name, device_settings in devices_config.items():
            selected_sources = device_settings.get("selected_61162_450", [])
            limit = device_settings.get("limit", 2)

            # 최소 2개, 최대 limit개까지 처리
            max_sensors = (
                max(2, min(len(selected_sources), limit)) if selected_sources else 2
            )

            # 각 센서별로 상태 조회
            for idx in range(max_sensors):
                sensor_idx = idx + 1  # 1부터 시작

                # 선택된 소스가 있는지 확인
                if idx < len(selected_sources):
                    source_id = selected_sources[idx]
                    sensor_name = f"{device_name}{sensor_idx} ({source_id})"
                else:
                    sensor_name = f"{device_name}{sensor_idx} (NONE)"
                    # NONE인 경우 빈 센텐스와 false 상태
                    sensors_status.append(
                        DeviceSensorStatus(name=sensor_name, sentences=[], status=False)
                    )
                    continue

                # Redis에서 해당 디바이스의 센텐스들 조회
                device_instance = f"{device_name}{sensor_idx}"
                pattern = f"{device_instance}:*"

                try:
                    redis_keys = redis_mgr.redis_conn.keys(pattern)
                except Exception as e:
                    # Redis 조회 실패 시 빈 센텐스로 처리
                    sensors_status.append(
                        DeviceSensorStatus(name=sensor_name, sentences=[], status=False)
                    )
                    continue

                # 발견된 센텐스들 추출 및 정리
                sentences = set()

                for key in redis_keys:
                    key_str = key.decode("utf-8") if isinstance(key, bytes) else key
                    # 키 형태: GPS1:GPGGA, GPS1:INGGA 등에서 센텐스 추출
                    if ":" in key_str:
                        sentence_part = key_str.split(":", 1)[1]  # GPGGA, INGGA 등
                        sentences.add(sentence_part)

                # 센텐스 리스트를 알파벳 순으로 정렬
                sorted_sentences = sorted(list(sentences))

                # 센서 상태 추가
                sensors_status.append(
                    DeviceSensorStatus(
                        name=sensor_name,
                        sentences=sorted_sentences,
                        status=len(sorted_sentences) > 0,
                    )
                )

        return DeviceSensorStatusResponse(sensors=sensors_status)

    except Exception as e:
        raise HTTPException(500, f"Failed to get device sensor status: {e}")


@router.get("/status", response_model=DeviceSensorStatusResponse)
async def get_devices_status(request: Request):
    """
    디바이스별 센서 상태 조회 API

    config_source_device.yaml 설정을 기반으로 현재 Redis에 저장된
    센서 데이터의 상태를 실시간으로 조회하여 반환합니다.

    요구사항:
    - Device마다 최소 2개, 최대 3개까지 출력
    - GPS1 (GP0001), GPS2 (GP0002) 형태로 표시
    - 선택되지 않은 경우 GPS2 (NONE) 표시
    - Receive Sensors는 Redis에서 실제 수신된 센텐스들 (알파벳 순)
    - STATUS는 sentences가 1개 이상이면 true, 아니면 false

    Returns:
        DeviceSensorStatusResponse: 디바이스별 센서 상태 정보
    """
    redis_mgr = request.app.state.redis_mgr
    return get_device_sensor_status_data(redis_mgr)


@router.get("/config", response_model=AllDeviceSettings)
async def get_device_config():
    raw = load_yaml_config(CONFIG_DEVICE_PATH)
    # assume raw is { "devices": { id1: {...}, id2: {...}, ... } }
    return AllDeviceSettings(**raw)


@router.put("/config")
async def set_all_devices(cfg: DeviceSettingsUpdateRequest):
    """
    디바이스 설정 업데이트 (metadata는 보존)

    사용자가 수정 가능한 devices 설정만 업데이트하고,
    시스템이 관리하는 metadata는 기존 값을 보존합니다.
    """
    # 기존 설정 로드
    existing_config = load_yaml_config(CONFIG_DEVICE_PATH)

    # devices만 업데이트, metadata는 그대로 유지
    existing_config["devices"] = {
        device_id: device.model_dump() for device_id, device in cfg.devices.items()
    }

    # 기존 포맷 보존하여 저장
    save_yaml(CONFIG_DEVICE_PATH, existing_config)
    return {"message": "devices settings updated (metadata preserved)"}


@router.get("/device/{device_id}", response_model=DeviceSetting)
async def get_one_device(
    device_id: str = Path(..., description="device key in devices dict")
):
    raw = load_yaml_config(CONFIG_DEVICE_PATH)
    devices: Dict[str, dict] = raw.get("devices", {})
    if device_id not in devices:
        raise HTTPException(404, f"device '{device_id}' not found")
    return DeviceSetting(**devices[device_id])


@router.put("/device/{device_id}", response_model=DeviceSetting)
async def set_one_device(
    device_id: str = Path(...),
    req: DeviceSetting = ...,  # type: ignore
):
    raw = load_yaml_config(CONFIG_DEVICE_PATH)
    devices: Dict[str, dict] = raw.setdefault("devices", {})
    # Pydantic validation happens here
    devices[device_id] = req.model_dump()
    save_yaml(CONFIG_DEVICE_PATH, raw)
    return req
