from fastapi import APIRouter, Request

router = APIRouter()


@router.get("/")
async def get_metrics(request: Request):
    """
    Returns:
      recv_dps_10s_avg : float  # 초당 수신 datagram 평균(10초)
      parse_sps_10s_avg: float  # 초당 파싱 성공 평균(10초)
      save_sps_10s_avg : float  # 초당 저장 성공 평균(10초)
      set_sps_10s_avg  : float  # 초당 SET 명령어 평균(10초)
      publish_sps_10s_avg: float  # 초당 PUBLISH 명령어 평균(10초)
      raw_backlog   : int    # 현재 raw queue 길이
    """
    return request.app.state.metrics
