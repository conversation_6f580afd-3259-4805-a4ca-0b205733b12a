# api/routers/admin.py
from fastapi import APIRouter, BackgroundTasks, status, Depends, HTTPException, Security
from fastapi.security import API<PERSON>eyHeader
from starlette.status import HTTP_401_UNAUTHORIZED

from utils.config import SHUTDOWN_TOKEN
import os
import signal
import time

router = APIRouter(tags=["admin"])

# 헤더에서 X-API-KEY 읽기
api_key_header = APIKeyHeader(name="X-API-KEY", auto_error=False)


def verify_shutdown_key(api_key: str = Security(api_key_header)):
    if not api_key or api_key != SHUTDOWN_TOKEN:
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing X-API-KEY",
        )


async def _shutdown_later():
    # 0.1초 기다려서 응답이 완전히 전송되도록 하고
    time.sleep(0.1)
    # 프로세스에 SIGTERM 보내기
    os.kill(os.getpid(), signal.SIGTERM)


@router.post(
    "/restart",
    status_code=status.HTTP_202_ACCEPTED,
    dependencies=[Depends(verify_shutdown_key)],
)
async def restart(background_tasks: BackgroundTasks):
    background_tasks.add_task(_shutdown_later)
    return {"detail": "Shutdown scheduled"}
