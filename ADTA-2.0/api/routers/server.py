"""
Server List API Router

서버 목록 조회 API를 제공합니다.
config/define_server_list.yaml 파일을 기반으로 서버 목록을 반환하며,
파일이 없을 경우 하드코딩된 기본값으로 파일을 생성합니다.
"""

import os
import yaml
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List
from utils.config_parser import load_yaml_config

router = APIRouter(prefix="/api/server", tags=["server"])

# 설정 파일 경로
SERVER_LIST_CONFIG_PATH = "config/define_server_list.yaml"


# ─── 응답 모델 ────────────────────────────────────
class ServerInfo(BaseModel):
    """서버 정보 모델"""
    name: str
    address: str
    port: int


class ServerListResponse(BaseModel):
    """서버 목록 응답 모델"""
    servers: List[ServerInfo]


# ─── 기본 서버 목록 데이터 ────────────────────────────────────
DEFAULT_SERVER_LIST = [
    # Multicast servers
    {"name": "MISC", "address": "***********", "port": 60001},
    {"name": "TGTD", "address": "***********", "port": 60002},
    {"name": "SATD", "address": "***********", "port": 60003},
    {"name": "NAVD", "address": "***********", "port": 60004},
    {"name": "VDRD", "address": "***********", "port": 60005},
    {"name": "RCOM", "address": "***********", "port": 60006},
    {"name": "TIME", "address": "***********", "port": 60007},
    {"name": "PROP", "address": "***********", "port": 60008},
    {"name": "USR1", "address": "***********", "port": 60009},
    {"name": "USR2", "address": "***********0", "port": 60010},
    {"name": "USR3", "address": "***********1", "port": 60011},
    {"name": "USR4", "address": "***********2", "port": 60012},
    {"name": "USR5", "address": "***********3", "port": 60013},
    {"name": "USR6", "address": "***********4", "port": 60014},
    {"name": "USR7", "address": "***********5", "port": 60015},
    {"name": "USR8", "address": "***********6", "port": 60016},
    {"name": "BAM1", "address": "***********7", "port": 60017},
    {"name": "BAM2", "address": "***********8", "port": 60018},
    {"name": "CAM1", "address": "***********9", "port": 60019},
    {"name": "CAM2", "address": "***********0", "port": 60020},
    {"name": "NETA", "address": "***********6", "port": 60056},
    {"name": "PGP1", "address": "***********7", "port": 60057},
    {"name": "PGP2", "address": "***********8", "port": 60058},
    {"name": "PGP3", "address": "***********9", "port": 60059},
    {"name": "PGP4", "address": "***********0", "port": 60060},
    {"name": "PGB1", "address": "***********1", "port": 60061},
    {"name": "PGB2", "address": "***********2", "port": 60062},
    {"name": "PGB3", "address": "***********3", "port": 60063},
    {"name": "PGB4", "address": "***********4", "port": 60064},
    
    # Unicast servers
    {"name": "VDR", "address": "***********", "port": 6501},
    {"name": "GYRO", "address": "***********", "port": 20001},
    {"name": "GPS", "address": "***********", "port": 20002},
    {"name": "AIS", "address": "***********", "port": 20003},
    {"name": "WIND", "address": "***********", "port": 20004},
    {"name": "RUDDER", "address": "***********", "port": 20005},
    {"name": "ECHO", "address": "***********", "port": 20006},
    {"name": "SPEED_LOG", "address": "***********", "port": 20007},
    {"name": "X_BAND_RADAR", "address": "***********", "port": 20008},
    {"name": "S_BAND_RADAR", "address": "***********", "port": 20009},
    {"name": "SATELITE_LOG", "address": "***********", "port": 20010},
]


# ─── 유틸리티 함수 ────────────────────────────────────
def create_default_server_config():
    """기본 서버 설정 파일을 생성합니다."""
    try:
        # config 디렉토리가 없으면 생성
        os.makedirs(os.path.dirname(SERVER_LIST_CONFIG_PATH), exist_ok=True)
        
        # YAML 파일 생성
        config_data = {"servers": DEFAULT_SERVER_LIST}
        with open(SERVER_LIST_CONFIG_PATH, "w", encoding="utf-8") as f:
            yaml.safe_dump(config_data, f, default_flow_style=False, allow_unicode=True)
        
        return config_data
    except Exception as e:
        raise HTTPException(500, f"Failed to create default server config: {e}")


def load_server_list():
    """서버 목록을 로드합니다. 파일이 없으면 기본값으로 생성합니다."""
    try:
        if not os.path.exists(SERVER_LIST_CONFIG_PATH):
            # 파일이 없으면 기본값으로 생성
            return create_default_server_config()
        
        # 기존 파일 로드
        config_data = load_yaml_config(SERVER_LIST_CONFIG_PATH)
        
        # servers 키가 없으면 기본값 사용
        if "servers" not in config_data:
            config_data = {"servers": DEFAULT_SERVER_LIST}
            # 파일 업데이트
            with open(SERVER_LIST_CONFIG_PATH, "w", encoding="utf-8") as f:
                yaml.safe_dump(config_data, f, default_flow_style=False, allow_unicode=True)
        
        return config_data
    except Exception as e:
        raise HTTPException(500, f"Failed to load server list: {e}")


# ─── API 엔드포인트 ────────────────────────────────────
@router.get("/list", response_model=List[ServerInfo])
async def get_server_list():
    """
    서버 목록 조회 API
    
    config/define_server_list.yaml 파일에서 서버 목록을 로드하여 반환합니다.
    파일이 없을 경우 하드코딩된 기본값으로 파일을 생성한 후 반환합니다.
    
    Returns:
        List[ServerInfo]: 서버 정보 목록 (name, address, port)
    """
    try:
        config_data = load_server_list()
        servers = config_data.get("servers", [])
        
        # ServerInfo 모델로 변환하여 검증
        server_list = [ServerInfo(**server) for server in servers]
        
        return server_list
    except Exception as e:
        raise HTTPException(500, f"Failed to get server list: {e}")
