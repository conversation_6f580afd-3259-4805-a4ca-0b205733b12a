#### api/routers/sensor.py (개선판)

import re
import yaml
import os
import glob
import shutil
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, Request
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel, validator
from utils.config import (
    CONFIG_SENSOR_PATH,
    DEFINE_SENTENCE_MAP_PATH,
    CONFIG_DEVICE_PATH,
)
from utils.config_parser import load_yaml_config, get_config
from model.sensor import Mode
from model.server import ServerConfig, ServerRules, PubSubSettings

router = APIRouter(prefix="/api/sensor", tags=["sensor"])


# ─── 공통 유틸 ────────────────────────────────────
def save_yaml(cfg: dict):
    try:
        with open(CONFIG_SENSOR_PATH, "w", encoding="utf-8") as f:
            yaml.safe_dump(cfg, f)
    except Exception as e:
        raise HTTPException(500, f"Cannot write sensor config: {e}")


# ─── 응답 모델 ────────────────────────────────────
class SensorConfigResponse(BaseModel):
    server: list[ServerConfig]
    rules: ServerRules
    mode: Mode
    hinas_sfi: Optional[str] = None
    seatrial_simulator_mode: Optional[ServerConfig] = None
    pubsub_settings: Optional[PubSubSettings] = None


# ─── 요청 모델 ────────────────────────────────────
class ModeRequest(BaseModel):
    mode: Mode


class HinasSfiRequest(BaseModel):
    hinas_sfi: str

    @validator("hinas_sfi")
    def check_sfi(cls, v):
        if not re.fullmatch(r"[A-Z]{2}\d{4}", v):
            raise ValueError("hinas_sfi must be 2 letters + 4 digits, e.g. XX0001")
        return v


class RulesRequest(BaseModel):
    rules: ServerRules


class ServerRequest(BaseModel):
    server: list[ServerConfig]


# ─── 디바이스별 센서 상태 응답 모델 ────────────────────────────────────
class DeviceSensorStatus(BaseModel):
    name: str  # "GPS1 (GP0001)" 또는 "GPS2 (NONE)"
    sentences: List[str]  # ["GPGGA", "GPGLL", "INGGA"] - 알파벳 순 정렬
    status: bool  # sentences가 1개 이상이면 True, 아니면 False


class DeviceSensorStatusResponse(BaseModel):
    sensors: List[DeviceSensorStatus]


# ─── 기능별 센서 상태 응답 모델 ────────────────────────────────────
class SentenceStatus(BaseModel):
    category: str  # "heading", "position" 등
    sentence: str  # "THS", "GGA" 등
    receivers: List[str]  # ["EI(ECDIS1, ECDIS2)", "IN(INS1)"] 등
    status: bool  # receivers가 1개 이상이면 True, 아니면 False


class FunctionalSensorStatusResponse(BaseModel):
    sensors: List[SentenceStatus]  # 플랫 리스트 구조


# ─── 기존 센서 상태 응답 모델 (하위 호환성) ────────────────────────────────────
class SensorSourceStatus(BaseModel):
    source_id: str
    device_instance: str
    talkers: List[str]
    status: bool


class SensorSentenceStatus(BaseModel):
    sentence: str
    status: bool
    sources: List[SensorSourceStatus]


class SensorDeviceStatus(BaseModel):
    device_name: str
    sentences: List[SensorSentenceStatus]


class SensorStatusResponse(BaseModel):
    devices: List[SensorDeviceStatus]


# ─── 프리셋 응답 모델 ────────────────────────────────────
class PresetListResponse(BaseModel):
    presets: List[str]


class PresetApplyResponse(BaseModel):
    message: str
    applied_preset: str


# ─── 프리셋 관련 함수 ────────────────────────────────────
def get_preset_templates() -> List[str]:
    """
    config/templates/ 폴더에서 config_sensor_*.yaml 파일들을 찾아서
    preset 이름 리스트를 반환
    """
    try:
        templates_dir = os.path.join(os.path.dirname(CONFIG_SENSOR_PATH), "templates")
        if not os.path.exists(templates_dir):
            return []

        # config_sensor_*.yaml 패턴으로 파일 검색
        pattern = os.path.join(templates_dir, "config_sensor_*.yaml")
        template_files = glob.glob(pattern)

        presets = []
        for file_path in template_files:
            filename = os.path.basename(file_path)
            # config_sensor_*.yaml에서 * 부분 추출
            if filename.startswith("config_sensor_") and filename.endswith(".yaml"):
                preset_name = filename[14:-5]  # "config_sensor_" 제거하고 ".yaml" 제거
                if preset_name:  # 빈 문자열이 아닌 경우만 추가
                    presets.append(preset_name)

        return sorted(presets)

    except Exception as e:
        raise HTTPException(500, f"Failed to get preset templates: {e}")


def apply_preset_template(preset_name: str) -> PresetApplyResponse:
    """
    선택한 preset 템플릿을 현재 config_sensor.yaml에 적용
    """
    try:
        # 1. 템플릿 파일 경로 생성
        templates_dir = os.path.join(os.path.dirname(CONFIG_SENSOR_PATH), "templates")
        template_path = os.path.join(templates_dir, f"config_sensor_{preset_name}.yaml")

        # 2. 템플릿 파일 존재 확인
        if not os.path.exists(template_path):
            raise HTTPException(404, f"Preset template '{preset_name}' not found")

        # 3. 템플릿 파일 유효성 검사 (YAML 파싱 테스트)
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise HTTPException(400, f"Template file '{preset_name}' is corrupted: {e}")
        except Exception as e:
            raise HTTPException(
                500, f"Failed to read template file '{preset_name}': {e}"
            )

        # 4. 현재 config_sensor.yaml 덮어쓰기
        try:
            shutil.copy2(template_path, CONFIG_SENSOR_PATH)
        except PermissionError:
            raise HTTPException(
                403, f"Permission denied: Cannot write to {CONFIG_SENSOR_PATH}"
            )
        except Exception as e:
            raise HTTPException(500, f"Failed to apply preset '{preset_name}': {e}")

        return PresetApplyResponse(
            message=f"Preset '{preset_name}' applied successfully",
            applied_preset=preset_name,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            500, f"Unexpected error while applying preset '{preset_name}': {e}"
        )


# ─── 새로운 센서 상태 조회 로직 ────────────────────────────────────
def get_device_sensor_status_data(redis_mgr) -> DeviceSensorStatusResponse:
    """
    config_source_device.yaml 기반으로 새로운 형태의 센서 상태를 조회하여 반환

    요구사항:
    1. Device마다 최소 2개, 최대 3개까지 출력 (선택된 개수에 따라)
    2. GPS1 (GP0001), GPS2 (GP0002) 형태로 표시
    3. 선택되지 않은 경우 GPS2 (NONE) 표시
    4. Receive Sensors는 Redis에서 실제 수신된 센텐스들 (알파벳 순)
    5. STATUS는 sentences가 1개 이상이면 true, 아니면 false
    """
    try:
        # 1. 디바이스 설정 로드
        device_config = load_yaml_config(CONFIG_DEVICE_PATH)
        devices_config = device_config.get("devices", {})

        # 2. Redis 연결 확인
        if not redis_mgr.is_connected:
            raise HTTPException(500, "Redis connection failed")

        sensors_status = []

        # 3. 각 디바이스별로 상태 조회
        for device_name, device_settings in devices_config.items():
            selected_sources = device_settings.get("selected_61162_450", [])
            limit = device_settings.get("limit", 2)

            # 최소 2개, 최대 limit개까지 처리
            max_sensors = (
                max(2, min(len(selected_sources), limit)) if selected_sources else 2
            )

            # 각 센서별로 상태 조회
            for idx in range(max_sensors):
                sensor_idx = idx + 1  # 1부터 시작

                # 선택된 소스가 있는지 확인
                if idx < len(selected_sources):
                    source_id = selected_sources[idx]
                    sensor_name = f"{device_name}{sensor_idx} ({source_id})"
                else:
                    sensor_name = f"{device_name}{sensor_idx} (NONE)"
                    # NONE인 경우 빈 센텐스와 false 상태
                    sensors_status.append(
                        DeviceSensorStatus(name=sensor_name, sentences=[], status=False)
                    )
                    continue

                # Redis에서 해당 디바이스의 센텐스들 조회
                device_instance = f"{device_name}{sensor_idx}"
                pattern = f"{device_instance}:*"

                try:
                    redis_keys = redis_mgr.redis_conn.keys(pattern)
                except Exception as e:
                    # Redis 조회 실패 시 빈 센텐스로 처리
                    sensors_status.append(
                        DeviceSensorStatus(name=sensor_name, sentences=[], status=False)
                    )
                    continue

                # 발견된 센텐스들 추출 및 정리
                sentences = set()

                for key in redis_keys:
                    key_str = key.decode("utf-8") if isinstance(key, bytes) else key
                    # 키 형태: GPS1:GPGGA, GPS1:INGGA 등에서 센텐스 추출
                    if ":" in key_str:
                        sentence_part = key_str.split(":", 1)[1]  # GPGGA, INGGA 등
                        sentences.add(sentence_part)

                # 센텐스 리스트를 알파벳 순으로 정렬
                sorted_sentences = sorted(list(sentences))

                # 센서 상태 추가
                sensors_status.append(
                    DeviceSensorStatus(
                        name=sensor_name,
                        sentences=sorted_sentences,
                        status=len(sorted_sentences) > 0,
                    )
                )

        return DeviceSensorStatusResponse(sensors=sensors_status)

    except Exception as e:
        raise HTTPException(500, f"Failed to get sensor status: {e}")


# ─── 기능별 센서 상태 조회 로직 ────────────────────────────────────
def get_functional_sensor_status_data(redis_mgr) -> FunctionalSensorStatusResponse:
    """
    define_data_sentence.yaml 기반으로 기능별 센서 상태를 조회하여 반환

    요구사항:
    1. define_data_sentence.yaml에서 카테고리별 sentence 목록 로드
    2. 각 sentence별로 Redis에서 실제 수신된 데이터 조회
    3. Talker 추출 및 그룹핑: ECDIS1:EITHS → EI(ECDIS1)
    4. 중복 sentence 허용 (heading과 stw에서 VHW 공통 사용)
    5. receivers가 비어있으면 status = false

    Returns:
        FunctionalSensorStatusResponse: 기능별 센서 상태 정보
    """
    try:
        # 1. define_data_sentence.yaml에서 카테고리별 sentence 목록 로드
        import yaml
        import os

        define_config_path = "config/define_data_sentence.yaml"
        if not os.path.exists(define_config_path):
            raise HTTPException(500, f"Config file not found: {define_config_path}")

        with open(define_config_path, "r", encoding="utf-8") as f:
            define_config = yaml.safe_load(f)
        sentence_data_map = define_config.get("sentence_data_map", {})

        # 2. Redis 연결 확인
        if not redis_mgr.is_connected:
            raise HTTPException(500, "Redis connection failed")

        # 3. 결과 구조 초기화 (플랫 리스트)
        sensors_result = []

        # 4. 각 카테고리별로 처리
        for category, sentences in sentence_data_map.items():
            # 각 sentence별로 처리
            for sentence in sentences:
                # Redis에서 해당 sentence를 포함하는 키들 조회
                pattern = f"*:*{sentence}"

                try:
                    redis_keys = redis_mgr.redis_conn.keys(pattern)
                except Exception as e:
                    # Redis 조회 실패 시 빈 receivers로 처리
                    sensors_result.append(
                        SentenceStatus(
                            category=category,
                            sentence=sentence,
                            receivers=[],
                            status=False,
                        )
                    )
                    continue

                # 5. Talker 추출 및 그룹핑
                talker_devices = {}  # {"EI": ["ECDIS1", "ECDIS2"], "IN": ["INS1"]}

                for key in redis_keys:
                    key_str = key.decode("utf-8") if isinstance(key, bytes) else key

                    if ":" not in key_str:
                        continue

                    # 키 분리: ECDIS1:EITHS → device=ECDIS1, full_sentence=EITHS
                    device, full_sentence = key_str.split(":", 1)

                    # Talker 추출: EITHS → EI (앞 2글자), THS (뒤 3글자)
                    if len(full_sentence) >= 3:
                        talker = full_sentence[:-3]  # 마지막 3글자 제거
                        sentence_part = full_sentence[-3:]  # 마지막 3글자

                        # 요청한 sentence와 일치하는지 확인
                        if sentence_part == sentence:
                            if talker not in talker_devices:
                                talker_devices[talker] = []
                            if device not in talker_devices[talker]:
                                talker_devices[talker].append(device)

                # 6. receivers 문자열 생성
                receivers = []
                for talker, devices in talker_devices.items():
                    if devices:
                        devices_str = ", ".join(sorted(devices))
                        receivers.append(f"{talker}({devices_str})")

                # 7. sentence 상태 추가
                sensors_result.append(
                    SentenceStatus(
                        category=category,
                        sentence=sentence,
                        receivers=sorted(receivers),  # 알파벳 순 정렬
                        status=len(receivers) > 0,
                    )
                )

        return FunctionalSensorStatusResponse(sensors=sensors_result)

    except Exception as e:
        raise HTTPException(500, f"Failed to get functional sensor status: {e}")


# ─── 기존 센서 상태 조회 로직 (하위 호환성) ────────────────────────────────────
def get_sensor_status_data(redis_mgr) -> SensorStatusResponse:
    """
    config_source_device.yaml 기반으로 센서 상태를 조회하여 반환
    """
    try:
        # 1. 디바이스 설정 로드
        device_config = load_yaml_config(CONFIG_DEVICE_PATH)
        devices_config = device_config.get("devices", {})

        # 2. Redis 연결 확인
        if not redis_mgr.is_connected:
            raise HTTPException(500, "Redis connection failed")

        devices_status = []

        # 3. 각 디바이스별로 상태 조회
        for device_name, device_settings in devices_config.items():
            available_sentences = device_settings.get("available_sentence", [])
            selected_sources = device_settings.get("selected_61162_450", [])

            sentences_status = []

            # 4. 각 sentence별로 상태 조회 (빈 리스트인 경우도 처리)
            if available_sentences:
                for sentence in available_sentences:
                    sources_status = []
                    sentence_has_data = False

                    # 5. 각 source별로 상태 조회
                    for idx, source_id in enumerate(selected_sources, start=1):
                        device_instance = f"{device_name}{idx}"

                        # Redis에서 해당 패턴의 키들을 조회
                        pattern = f"{device_instance}:*{sentence}"
                        redis_keys = redis_mgr.redis_conn.keys(pattern)

                        # 발견된 talker들 추출
                        talkers = []
                        source_has_data = False

                        for key in redis_keys:
                            key_str = (
                                key.decode("utf-8") if isinstance(key, bytes) else key
                            )
                            # 키 형태: GPS1:GPGGA, GPS1:INGGA 등에서 talker 추출
                            if ":" in key_str:
                                talker_sentence = key_str.split(":", 1)[1]
                                if talker_sentence.endswith(sentence):
                                    talker = talker_sentence[: -len(sentence)]
                                    if talker and talker not in talkers:
                                        talkers.append(talker)
                                        source_has_data = True
                                        sentence_has_data = True

                        sources_status.append(
                            SensorSourceStatus(
                                source_id=source_id,
                                device_instance=device_instance,
                                talkers=sorted(talkers),
                                status=source_has_data,
                            )
                        )

                    sentences_status.append(
                        SensorSentenceStatus(
                            sentence=sentence,
                            status=sentence_has_data,
                            sources=sources_status,
                        )
                    )

            # available_sentence가 비어있어도 디바이스는 포함 (빈 sentences 리스트로)
            devices_status.append(
                SensorDeviceStatus(device_name=device_name, sentences=sentences_status)
            )

        return SensorStatusResponse(devices=devices_status)

    except Exception as e:
        raise HTTPException(500, f"Failed to get sensor status: {e}")


# ─── 엔드포인트 ────────────────────────────────────
@router.get("/config", response_model=SensorConfigResponse)
async def get_sensor_config():
    try:
        servers, rules, mode, vdm, pubsub_settings = get_config(CONFIG_SENSOR_PATH)
        # hinas_sfi 필드 추가로 읽기
        raw_config = load_yaml_config(CONFIG_SENSOR_PATH)
        hinas_sfi = raw_config.get("hinas_sfi")
    except Exception as e:
        raise HTTPException(500, f"Failed to parse sensor config: {e}")
    return SensorConfigResponse(
        server=servers,
        rules=rules,
        mode=mode,
        hinas_sfi=hinas_sfi,
        seatrial_simulator_mode=vdm,
        pubsub_settings=pubsub_settings,
    )


@router.get("/data/config")
async def get_sensor_data_config():
    try:
        raw = load_yaml_config(DEFINE_SENTENCE_MAP_PATH)
        if "sentence_data_map" in raw:
            raw = raw["sentence_data_map"]
        else:
            raise HTTPException(404, "No 'sentence_data_map' found in the config file.")
    except Exception as e:
        raise HTTPException(500, f"Failed to parse sensor config: {e}")
    return raw


@router.put("/mode")
async def set_mode(req: ModeRequest):
    raw = load_yaml_config(CONFIG_SENSOR_PATH)
    raw["mode"] = req.mode.value
    save_yaml(raw)
    return {"message": "mode updated"}


@router.put("/hinas_sfi")
async def set_hinas_sfi(req: HinasSfiRequest):
    raw = load_yaml_config(CONFIG_SENSOR_PATH)
    raw["hinas_sfi"] = req.hinas_sfi
    save_yaml(raw)
    return {"message": "hinas_sfi updated"}


@router.put("/rules")
async def set_rules(req: RulesRequest):
    raw = load_yaml_config(CONFIG_SENSOR_PATH)
    raw["rules"] = req.rules.dict()
    save_yaml(raw)
    return {"message": "rules updated"}


@router.put("/server")
async def set_server(req: ServerRequest):
    raw = load_yaml_config(CONFIG_SENSOR_PATH)
    # raw["server"] = [s.dict() for s in req.server]
    raw["server"] = jsonable_encoder(req.server)
    save_yaml(raw)
    return {"message": "server updated"}


# 디바이스별 센서 상태 API는 /api/devices/status로 이동됨


@router.get("/status", response_model=FunctionalSensorStatusResponse)
async def get_sensor_status(request: Request):
    """
    기능별 센서 상태 조회 API (플랫 리스트 구조)

    define_data_sentence.yaml 설정을 기반으로 현재 Redis에 저장된
    센서 데이터의 상태를 플랫 리스트 형태로 조회하여 반환합니다.

    요구사항:
    - 플랫 리스트 구조: 각 항목에 category 필드 포함
    - Talker 추출 및 그룹핑: ECDIS1:EITHS → EI(ECDIS1)
    - 중복 sentence 허용 (heading과 stw에서 VHW 공통 사용)
    - receivers가 비어있으면 status = false

    Returns:
        FunctionalSensorStatusResponse: 플랫 리스트 형태의 센서 상태 정보
    """
    redis_mgr = request.app.state.redis_mgr
    return get_functional_sensor_status_data(redis_mgr)


@router.get("/status/legacy", response_model=SensorStatusResponse)
async def get_sensor_status_legacy(request: Request):
    """
    기존 센서 상태 조회 API (하위 호환성)

    config_source_device.yaml 설정을 기반으로 현재 Redis에 저장된
    센서 데이터의 상태를 실시간으로 조회하여 반환합니다.

    Returns:
        SensorStatusResponse: 각 디바이스별 sentence와 talker 상태 정보
    """
    redis_mgr = request.app.state.redis_mgr
    return get_sensor_status_data(redis_mgr)


@router.get("/presets", response_model=PresetListResponse)
async def get_presets():
    """
    사용 가능한 센서 설정 프리셋 목록 조회

    config/templates/ 폴더에서 config_sensor_*.yaml 파일들을 찾아서
    프리셋 이름 리스트를 반환합니다.

    Returns:
        PresetListResponse: 사용 가능한 프리셋 이름들의 리스트
    """
    presets = get_preset_templates()
    return PresetListResponse(presets=presets)


@router.post("/preset/{preset_name}", response_model=PresetApplyResponse)
async def apply_preset(preset_name: str):
    """
    선택한 프리셋을 현재 센서 설정에 적용

    지정된 프리셋 템플릿을 현재 config_sensor.yaml 파일에 적용합니다.
    기존 설정은 완전히 덮어쓰여집니다.

    Args:
        preset_name: 적용할 프리셋 이름 (예: multicast, unicast)

    Returns:
        PresetApplyResponse: 적용 결과 메시지와 적용된 프리셋 이름
    """
    return apply_preset_template(preset_name)
