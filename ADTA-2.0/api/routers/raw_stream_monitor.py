"""
실시간 RAW 패킷 SSE 스트리밍 모니터링

Server-Sent Events를 통한 실시간 tcpdump 스타일 패킷 모니터링
"""

import os
import time
import asyncio
import threading
import logging
from datetime import datetime
from typing import Dict, Set, Optional
from fastapi import APIRouter, HTTPException, Request, Query, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import json
from collections import deque
from utils.redis_client import redis_mgr

router = APIRouter(prefix="/api/raw-monitor", tags=["raw-stream-monitor"])

# ─── 환경변수 설정 ────────────────────────────────────
RAW_MONITOR_AUTH_ENABLED = (
    os.getenv("RAW_MONITOR_AUTH_ENABLED", "false").lower() == "true"
)
RAW_MONITOR_AUTH_TOKEN = os.getenv("RAW_MONITOR_AUTH_TOKEN", "default_secret_token")
RAW_MONITOR_MAX_DURATION = int(os.getenv("RAW_MONITOR_MAX_DURATION", "60"))  # 기본 60초
RAW_MONITOR_MAX_DURATION_LIMIT = int(
    os.getenv("RAW_MONITOR_MAX_DURATION_LIMIT", "300")
)  # 최대 5분
RAW_MONITOR_MAX_CONNECTIONS = int(
    os.getenv("RAW_MONITOR_MAX_CONNECTIONS", "3")
)  # 최대 3개 연결
RAW_MONITOR_BUFFER_SIZE = int(os.getenv("RAW_MONITOR_BUFFER_SIZE", "1000"))  # 버퍼 크기

# ─── 전역 변수 ────────────────────────────────────
active_connections: Set[str] = set()  # 활성 연결 ID 집합
connection_lock = threading.RLock()
packet_buffer = deque(maxlen=RAW_MONITOR_BUFFER_SIZE)  # 패킷 버퍼
buffer_lock = threading.RLock()

# ─── Redis 연결 (프로세스 간 상태 공유용) ────────────────────────────────────
try:
    redis_client = redis.Redis(host="localhost", port=6379, db=0, decode_responses=True)
    redis_client.ping()  # 연결 테스트
    REDIS_AVAILABLE = True
    logging.info(
        "[RawStreamMonitor] Redis connection established for cross-process communication"
    )
except Exception as e:
    redis_client = None
    REDIS_AVAILABLE = False
    logging.warning(
        f"[RawStreamMonitor] Redis not available, using local state only: {e}"
    )

REDIS_KEY_ACTIVE_CONNECTIONS = "raw_monitor:active_connections"


# ─── 응답 모델 ────────────────────────────────────
class StreamStatus(BaseModel):
    active_connections: int
    max_connections: int
    buffer_size: int
    max_duration: int
    auth_enabled: bool


# ─── Auth 의존성 ────────────────────────────────────
async def verify_auth_token(auth_token: Optional[str] = Query(None)):
    """인증 토큰 검증 (환경변수로 활성화/비활성화 가능)"""
    if not RAW_MONITOR_AUTH_ENABLED:
        return True  # 인증 비활성화 시 통과

    if not auth_token:
        raise HTTPException(401, "Authentication token required")

    if auth_token != RAW_MONITOR_AUTH_TOKEN:
        raise HTTPException(401, "Invalid authentication token")

    return True


# ─── 연결 관리 ────────────────────────────────────
def generate_connection_id() -> str:
    """고유한 연결 ID 생성"""
    return f"conn_{int(time.time() * 1000)}_{id(threading.current_thread())}"


def add_connection(connection_id: str) -> bool:
    """새 연결 추가 (최대 연결 수 체크, Redis 기반)"""
    with connection_lock:
        # 로컬 상태 업데이트
        if len(active_connections) >= RAW_MONITOR_MAX_CONNECTIONS:
            logging.warning(
                f"[RawStreamMonitor] Connection limit reached: {len(active_connections)}/{RAW_MONITOR_MAX_CONNECTIONS}"
            )
            return False
        active_connections.add(connection_id)

        # 파일 기반 상태 업데이트 (프로세스 간 공유)
        try:
            import os

            status_file = "/tmp/raw_monitor_active.flag"
            with open(status_file, "w") as f:
                f.write(f"{connection_id}\n")
        except Exception as e:
            logging.error(f"[RawStreamMonitor] File connection add error: {e}")

        logging.info(
            f"[RawStreamMonitor] 🔗 NEW CONNECTION: {connection_id} ({len(active_connections)}/{RAW_MONITOR_MAX_CONNECTIONS})"
        )

        # 첫 번째 연결 시 추가 로그
        if len(active_connections) == 1:
            logging.info(
                "[RawStreamMonitor] 🎯 FIRST CONNECTION: packet buffering will now start"
            )
            # 파일 상태도 로그
            try:
                status_file = "/tmp/raw_monitor_active.flag"
                with open(status_file, "r") as f:
                    content = f.read().strip()
                logging.info(
                    f"[RawStreamMonitor] 📁 STATUS FILE CREATED: {status_file} -> {content}"
                )
            except Exception as e:
                logging.error(f"[RawStreamMonitor] ❌ STATUS FILE ERROR: {e}")
        return True


def remove_connection(connection_id: str):
    """연결 제거 (Redis 기반)"""
    with connection_lock:
        # 로컬 상태 업데이트
        active_connections.discard(connection_id)

        # 파일 기반 상태 업데이트 (프로세스 간 공유)
        if len(active_connections) == 0:  # 마지막 연결이 제거되면 파일 삭제
            try:
                import os

                status_file = "/tmp/raw_monitor_active.flag"
                if os.path.exists(status_file):
                    os.remove(status_file)
            except Exception as e:
                logging.error(f"[RawStreamMonitor] File connection remove error: {e}")

        logging.info(
            f"[RawStreamMonitor] Connection closed: {connection_id} ({len(active_connections)}/{RAW_MONITOR_MAX_CONNECTIONS})"
        )


# ─── 패킷 데이터 관리 ────────────────────────────────────
class RawPacketData:
    def __init__(
        self,
        server_name: str,
        server_port: int,
        destination: str,
        raw_data: bytes,
        timestamp: float,
    ):
        self.server_name = server_name
        self.server_port = server_port
        self.destination = destination
        self.raw_data = raw_data
        self.timestamp = timestamp

    def format_for_sse(self) -> str:
        """SSE 형태로 패킷 포맷팅"""
        try:
            # 타임스탬프
            dt = datetime.fromtimestamp(self.timestamp)
            timestamp_str = dt.strftime("%H:%M:%S.%f")[:-3]

            # 서버 정보
            server_info = f"{self.server_name}:{self.server_port}"
            dest_info = f" -> {self.destination}" if self.destination else ""

            # Raw 데이터 디코딩 (더 강건하게)
            try:
                if isinstance(self.raw_data, bytes):
                    # 바이너리 데이터 처리
                    raw_content = self.raw_data.decode(
                        "utf-8", errors="replace"
                    ).strip()
                    # 제어 문자 제거
                    raw_content = "".join(
                        (
                            char
                            if ord(char) >= 32 or char in "\r\n\t"
                            else f"\\x{ord(char):02x}"
                        )
                        for char in raw_content
                    )
                    # 줄바꿈 처리
                    raw_content = (
                        raw_content.replace("\r\n", "\\r\\n")
                        .replace("\n", "\\n")
                        .replace("\r", "\\r")
                    )
                else:
                    raw_content = str(self.raw_data)

                # 너무 긴 패킷은 자르기
                if len(raw_content) > 1000:
                    raw_content = raw_content[:1000] + "...[TRUNCATED]"

            except Exception as decode_error:
                # 디코딩 실패 시 hex 표시
                try:
                    hex_data = (
                        self.raw_data.hex()
                        if isinstance(self.raw_data, bytes)
                        else str(self.raw_data)
                    )
                    raw_content = (
                        f"[HEX] {hex_data[:200]}{'...' if len(hex_data) > 200 else ''}"
                    )
                except:
                    raw_content = f"[DECODE_ERROR] {decode_error}"

            # SSE 형태로 포맷 (실제 줄바꿈 문자 사용)
            header = f"[{timestamp_str}] {server_info}{dest_info}"
            return f"data: {header}\ndata: {raw_content}\n\n"

        except Exception as e:
            logging.error(f"[RawStreamMonitor] Packet formatting error: {e}")
            return f"data: [FORMAT_ERROR] {e}\\n\\n"


def add_packet_to_buffer(
    server_name: str, server_port: int, destination: str, raw_data: bytes
):
    """패킷을 버퍼에 추가 (강건한 에러 처리)"""
    # 🔥 프로세스 간 통신: 파일 기반 큐를 통해 실제 패킷 전달
    # receiver.py에서 받은 실제 패킷을 파일로 저장하고 API 서버에서 읽어옴

    # 🚀 Redis 기반 실시간 패킷 큐 (고성능)
    try:
        r = redis_mgr.redis_conn

        packet_data = {
            "server_name": server_name,
            "server_port": server_port,
            "destination": destination,
            "raw_data": raw_data.hex(),  # bytes를 hex string으로 변환
            "timestamp": time.time(),
        }

        # Redis List에 패킷 추가 (LPUSH로 최신 패킷이 앞에 오도록)
        r.lpush("raw_packet_queue", json.dumps(packet_data))

        # 큐 크기 제한 (최대 1000개, 오래된 것 자동 삭제)
        r.ltrim("raw_packet_queue", 0, 999)

    except Exception as e:
        # Redis 실패 시 로그 (첫 번째만)
        if not hasattr(add_packet_to_buffer, "_redis_error_logged"):
            logging.error(f"[RawStreamMonitor] Redis queue error: {e}")
            add_packet_to_buffer._redis_error_logged = True

    try:
        # 입력 검증
        if not isinstance(raw_data, bytes):
            logging.warning(
                f"[RawStreamMonitor] Invalid data type: {type(raw_data)}, converting to bytes"
            )
            raw_data = str(raw_data).encode("utf-8", errors="replace")

        if len(raw_data) == 0:
            return  # 빈 패킷은 무시

        # 버퍼에 추가
        with buffer_lock:
            packet = RawPacketData(
                server_name=str(server_name),
                server_port=int(server_port),
                destination=str(destination),
                raw_data=raw_data,
                timestamp=time.time(),
            )
            packet_buffer.append(packet)

            # 디버그 로그 (모든 패킷 버퍼링 확인)
            if len(packet_buffer) <= 10:
                logging.info(
                    f"[RawStreamMonitor] 📦 PACKET BUFFERED #{len(packet_buffer)}: {server_name}:{server_port} ({len(raw_data)} bytes)"
                )

    except Exception as e:
        logging.error(f"[RawStreamMonitor] Error adding packet to buffer: {e}")
        logging.error(
            f"[RawStreamMonitor] Packet details: server={server_name}, port={server_port}, data_len={len(raw_data) if raw_data else 0}"
        )


def get_buffered_packets() -> list:
    """버퍼된 패킷들 가져오기 및 클리어 (강력한 디버깅)"""
    with buffer_lock:
        buffer_size_before = len(packet_buffer)
        packets = list(packet_buffer)
        packet_buffer.clear()
        buffer_size_after = len(packet_buffer)

        # 디버깅 로그
        if buffer_size_before > 0:
            logging.debug(
                f"[RawStreamMonitor] 🔥 BUFFER GET: {buffer_size_before} packets retrieved, buffer cleared ({buffer_size_after} remaining)"
            )
        else:
            # 빈 버퍼도 가끔 로그
            if not hasattr(get_buffered_packets, "_empty_count"):
                get_buffered_packets._empty_count = 0
            get_buffered_packets._empty_count += 1

            if get_buffered_packets._empty_count % 50 == 1:  # 50번마다 한 번
                logging.info(
                    f"[RawStreamMonitor] BUFFER EMPTY: No packets in buffer (check #{get_buffered_packets._empty_count})"
                )

        return packets


# ─── SSE 스트리밍 ────────────────────────────────────
async def packet_stream_generator(connection_id: str, max_duration: int):
    """패킷 스트림 생성기"""
    start_time = time.time()
    last_heartbeat = time.time()

    try:
        # 스트림 시작 메시지
        logging.info(f"[RawStreamMonitor] 🚀 SSE STREAM STARTED: {connection_id}")
        start_msg = f"data: [STREAM_START] Connection {connection_id} established\n\n"
        yield start_msg

        loop_count = 0
        while True:
            loop_count += 1
            current_time = time.time()

            # 최대 지속 시간 체크
            if current_time - start_time >= max_duration:
                yield f"data: [STREAM_END] Maximum duration ({max_duration}s) reached\n\n"
                break

            # 연결 상태 체크
            if connection_id not in active_connections:
                yield f"data: [STREAM_END] Connection terminated\n\n"
                break

            # � Redis 큐에서 실시간 패킷 읽어오기 (고성능)
            try:
                r = redis_mgr.redis_conn

                # Redis List에서 패킷 가져오기 (RPOP으로 오래된 것부터)
                # 최대 20개까지 한 번에 처리 (실시간성 보장)
                packets_processed = 0
                while packets_processed < 20:
                    packet_json = r.rpop("raw_packet_queue")
                    if not packet_json:
                        break

                    try:
                        packet_data = json.loads(packet_json)

                        # hex string을 bytes로 변환
                        raw_data = bytes.fromhex(packet_data["raw_data"])

                        # RawPacketData 객체 생성
                        packet = RawPacketData(
                            server_name=packet_data["server_name"],
                            server_port=packet_data["server_port"],
                            destination=packet_data["destination"],
                            raw_data=raw_data,
                            timestamp=packet_data["timestamp"],
                        )

                        # 버퍼에 추가
                        with buffer_lock:
                            packet_buffer.append(packet)

                        packets_processed += 1

                    except Exception as e:
                        # 잘못된 패킷 데이터는 무시
                        continue

            except Exception as e:
                # Redis 처리 실패 시 로그 (첫 번째만)
                if not hasattr(packet_stream_generator, "_redis_error_logged"):
                    logging.error(f"[RawStreamMonitor] Redis processing error: {e}")
                    packet_stream_generator._redis_error_logged = True

            # 버퍼된 패킷들 전송
            packets = get_buffered_packets()
            if packets:
                for packet in packets:
                    sse_data = packet.format_for_sse()
                    yield sse_data
                last_heartbeat = current_time
            else:
                # 패킷이 없으면 heartbeat (10초마다)
                if current_time - last_heartbeat > 10:
                    heartbeat_msg = (
                        f"data: [HEARTBEAT] {datetime.now().strftime('%H:%M:%S')}\n\n"
                    )
                    yield heartbeat_msg
                    last_heartbeat = current_time

            # 🚀 실시간성 향상: 더 짧은 대기 시간
            # 패킷이 있으면 즉시 처리, 없으면 짧게 대기
            if packets:
                await asyncio.sleep(0.01)  # 10ms - 매우 빠른 처리
            else:
                await asyncio.sleep(0.05)  # 50ms - 적당한 대기

    except asyncio.CancelledError:
        yield f"data: [STREAM_END] Connection cancelled\n\n"
    except Exception as e:
        yield f"data: [STREAM_ERROR] {e}\n\n"
    finally:
        remove_connection(connection_id)


# ─── API 엔드포인트 ────────────────────────────────────
@router.get("/stream")
async def stream_raw_packets(
    request: Request,
    max_duration: int = Query(
        RAW_MONITOR_MAX_DURATION,
        ge=10,
        le=RAW_MONITOR_MAX_DURATION_LIMIT,
        description=f"스트림 지속 시간 (10-{RAW_MONITOR_MAX_DURATION_LIMIT}초)",
    ),
    auth_verified: bool = Depends(verify_auth_token),
):
    """
    실시간 RAW 패킷 SSE 스트리밍

    Server-Sent Events를 통해 실시간으로 tcpdump 스타일 패킷을 전송합니다.

    Args:
        max_duration: 최대 스트림 지속 시간 (초)
        auth_token: 인증 토큰 (환경변수로 활성화 시 필요)

    Returns:
        StreamingResponse: SSE 스트림
    """
    # 연결 ID 생성
    connection_id = generate_connection_id()

    # 최대 연결 수 체크
    if not add_connection(connection_id):
        raise HTTPException(
            429,
            f"Too many active connections. Maximum {RAW_MONITOR_MAX_CONNECTIONS} connections allowed.",
        )

    # 설정값 검증 및 제한
    max_duration = min(max_duration, RAW_MONITOR_MAX_DURATION_LIMIT)
    max_duration = max(max_duration, 10)  # 최소 10초

    logging.info(
        f"[RawStreamMonitor] Starting stream for {connection_id}, duration: {max_duration}s"
    )

    return StreamingResponse(
        packet_stream_generator(connection_id, max_duration),
        media_type="text/event-stream",  # SSE 표준 미디어 타입
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # nginx 버퍼링 비활성화
            "Access-Control-Allow-Origin": "*",  # CORS
            "Access-Control-Allow-Headers": "Cache-Control",
            "Transfer-Encoding": "chunked",  # 청크 전송
        },
    )


@router.get("/stream/status", response_model=StreamStatus)
async def get_stream_status():
    """
    스트림 상태 조회
    """
    with connection_lock:
        active_count = len(active_connections)

    with buffer_lock:
        buffer_size = len(packet_buffer)

    return StreamStatus(
        active_connections=active_count,
        max_connections=RAW_MONITOR_MAX_CONNECTIONS,
        buffer_size=buffer_size,
        max_duration=RAW_MONITOR_MAX_DURATION,
        auth_enabled=RAW_MONITOR_AUTH_ENABLED,
    )


@router.post("/stream/disconnect/{connection_id}")
async def disconnect_stream(connection_id: str):
    """
    특정 스트림 연결 강제 종료
    """
    with connection_lock:
        if connection_id in active_connections:
            active_connections.remove(connection_id)
            logging.info(f"[RawStreamMonitor] Forcefully disconnected: {connection_id}")
            return {"message": f"Connection {connection_id} disconnected"}
        else:
            raise HTTPException(404, f"Connection {connection_id} not found")


# ─── 패킷 수집 함수 (receiver에서 호출) ────────────────────────────────────
def has_active_connections() -> bool:
    """활성 연결이 있는지 확인 (파일 기반, 프로세스 간 공유)"""
    # 로컬 연결 확인
    if active_connections:
        return True

    # 파일 기반 연결 확인 (다른 프로세스의 연결)
    try:
        import os

        status_file = "/tmp/raw_monitor_active.flag"
        if os.path.exists(status_file):
            # 파일이 5분 이상 오래되면 삭제 (정리)
            import time

            if time.time() - os.path.getmtime(status_file) > 300:
                os.remove(status_file)
                return False
            return True
    except Exception as e:
        logging.error(f"[RawStreamMonitor] File connection check error: {e}")

    return False


def add_raw_packet_for_stream(
    server_name: str, server_port: int, destination: str, raw_data: bytes
):
    """
    Receiver에서 호출하는 패킷 추가 함수
    활성 연결이 있을 때만 버퍼링 (강건한 에러 처리)
    """
    try:
        # 디버깅: 함수 호출 확인
        if not hasattr(add_raw_packet_for_stream, "_call_count"):
            add_raw_packet_for_stream._call_count = 0
        add_raw_packet_for_stream._call_count += 1

        # 활성 연결 확인 (Redis 기반)
        has_connections = has_active_connections()

        # 처음 몇 번 호출 시 상세 로그
        if add_raw_packet_for_stream._call_count <= 3:
            local_count = len(active_connections)
            file_exists = False
            try:
                import os

                file_exists = os.path.exists("/tmp/raw_monitor_active.flag")
            except:
                pass
            logging.info(
                f"[RawStreamMonitor] Call #{add_raw_packet_for_stream._call_count}: {server_name}:{server_port}, local_connections={local_count}, file_exists={file_exists}, has_connections={has_connections}"
            )

        # 🔥 실제 센서 패킷도 항상 버퍼링 (테스트용)
        # 연결 상태와 관계없이 모든 패킷을 버퍼링

        # 함수 호출 카운터 (디버깅용)
        if not hasattr(add_raw_packet_for_stream, "_call_counter"):
            add_raw_packet_for_stream._call_counter = 0
        add_raw_packet_for_stream._call_counter += 1

        add_packet_to_buffer(server_name, server_port, destination, raw_data)

        # 🔥 실제 센서 패킷 로그 (처음 10개만)
        if add_raw_packet_for_stream._call_counter <= 10:
            logging.info(
                f"[REAL SENSOR #{add_raw_packet_for_stream._call_counter}] {server_name}:{server_port}→{destination}] {raw_data}"
            )

        # 첫 번째 패킷 수신 시 로그
        if not hasattr(add_raw_packet_for_stream, "_first_packet_logged"):
            logging.info(
                f"[RawStreamMonitor] 🎯 REAL PACKET BUFFERING ENABLED: All packets will be buffered"
            )
            add_raw_packet_for_stream._first_packet_logged = True

        # 기존 조건부 처리는 더 이상 필요 없음 (모든 패킷을 버퍼링하므로)

    except Exception as e:
        # 에러가 발생해도 receiver 동작에 영향을 주지 않도록 조용히 처리
        if not hasattr(add_raw_packet_for_stream, "_error_logged"):
            logging.error(f"[RawStreamMonitor] Error in add_raw_packet_for_stream: {e}")
            add_raw_packet_for_stream._error_logged = True
