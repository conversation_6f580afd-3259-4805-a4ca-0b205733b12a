from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routers import health, metrics
from .routers import admin, error
from .routers import sensor, device, packet_monitor, oauth2, server, raw_stream_monitor

app = FastAPI(
    title="ADTA Service",
    version="1.1.0",
    docs_url="/api/docs",  # Swagger UI
    redoc_url="/api/redoc",  # ReDoc UI
    openapi_url="/api/openapi.json",  # OpenAPI JSON
)

# CORS 미들웨어 추가
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 라우터 등록
app.include_router(health.router, prefix="/health")
app.include_router(metrics.router, prefix="/metrics")
app.include_router(admin.router, prefix="/admin")
app.include_router(error.router)
app.include_router(device.router)
app.include_router(sensor.router)
app.include_router(server.router)
app.include_router(packet_monitor.router)
app.include_router(raw_stream_monitor.router)
app.include_router(oauth2.router)
