# 파일명: helpers.py

import socket
import logging
from typing import Tuple, Optional

from pipeline.relay import relay
from model.server import (
    ServerConfig,
    EndpointRule,
    SourceRule,
    SentenceRule,
    ProtocolVersion,
)


def _parse_host_port(dest: str):  # -> Tuple[str, int]:
    """
    "hostname:port" 형태의 문자열을 호스트(str), 포트(int)로 분리.
    잘못된 형식일 경우 ValueError를 발생시킴.
    """
    try:
        host, port_str = dest.rsplit(":", 1)
        return host, int(port_str)
    except Exception as e:
        raise ValueError(f"잘못된 relay 목적지 형식: {dest}") from e


def _relay_to(dest: str, raw_data: bytes):
    """
    UDP 소켓을 재사용하여 raw_data를 dest("host:port")로 전송.
    """
    try:
        host, port = _parse_host_port(dest)
        relay.relay(host, port, raw_data)
    except Exception:
        logging.exception(f"[Helper Relay 실패] dest={dest}, data={raw_data!r}")


def apply_endpoint_rule(
    srv: ServerConfig,
    raw_data: bytes,
):  # -> <PERSON><PERSON>[bool, bool]:
    """
    • srv.endpoint_rules 기준으로 “파싱 전”에 전체(raw) 패킷을 ignore/relay 여부 결정.
    • 반환: (should_ignore, should_relay)
    """
    ep_rule: EndpointRule = srv.endpoint_rules  # type: ignore

    # 1) ignore 우선 검사
    if ep_rule.ignore:
        logging.debug(f"[Endpoint Ignore] {srv.address}:{srv.port} → 전체 폐기")
        return True, False

    # 2) relay가 지정되어 있으면 원시 패킷 전송
    if ep_rule.relay:
        _relay_to(ep_rule.relay, raw_data)
        logging.debug(f"[Endpoint Relay] {srv.address}:{srv.port} → 원시 중계")
        return False, True

    # 3) 둘 다 아닌 경우 → 이후 파싱 단계로 넘김
    return False, False


def apply_source_rule(
    srv: ServerConfig,
    source_rule_map: dict[str, SourceRule],
    parsed,
    raw_line: bytes,
):  # -> Tuple[bool, bool]:
    """
    • parsed_obj.tag['source_id'] 값으로 source_rules를 검사.
    • 반환: (should_ignore, should_relay)
    """
    if srv.message_protocol != ProtocolVersion.V450:
        return False, False
    if parsed.tag is None:
        logging.debug(f"[{srv.message_protocol}] Tag is None: {parsed.sentence_name}")
        return False, False

    try:
        source_id = str(parsed.tag.source_id)
    except Exception as e:
        logging.error(f"[{srv.message_protocol}] Cannot Found Source ID Error: {e}")
        logging.error(f"[{srv}] {parsed}")
        return False, False

    rule = source_rule_map.get(source_id)
    if not rule:
        return False, False

    is_ignore = bool(rule.ignore)
    is_relay = bool(rule.relay)

    if is_ignore:
        logging.debug(f"[Source Ignore] source_id={source_id} → Ignore")
    if rule.relay is not None:
        _relay_to(rule.relay, raw_line)
        logging.debug(f"[Source Relay] source_id={source_id} → Relay {raw_line}")

    return is_ignore, is_relay


def apply_sentence_rule(
    sentence_rule_map: dict[str, SentenceRule], parsed_obj, raw_line: bytes
):  # -> Tuple[bool, bool]:
    """
    • parsed_obj.sentence(문장 ID)로 sentence_rules를 검사.
    • 반환: (should_ignore, should_relay)
    """
    # parsed_obj.sentence 속성으로 문장 ID 가져오기
    sentence_name = getattr(parsed_obj, "sentence", None)
    if sentence_name is None:
        return False, False

    rule = sentence_rule_map.get(sentence_name)
    if not rule:
        return False, False

    is_ignore = bool(rule.ignore)
    is_relay = bool(rule.relay)

    if rule.ignore:
        logging.debug(f"[Sentence Ignore] sentence={sentence_name} → Ignore")
    if rule.relay is not None:
        _relay_to(rule.relay, raw_line)
        logging.debug(f"[Sentence Relay] sentence={sentence_name} → Relay")

    return is_ignore, is_relay
