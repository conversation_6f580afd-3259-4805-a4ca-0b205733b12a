import os
import logging
import time
import queue
import threading
from datetime import datetime
from collections import namedtuple

from pipeline.helpers import (
    apply_endpoint_rule,
    apply_source_rule,
    apply_sentence_rule,
)

from model.sensor import Mode
from model.server import ServerConfig, ServerRules, ProtocolVersion, PubSubSettings
from model.device import AllDeviceSettings

from avikus_parser.iec_61162_1 import Iec61162_1
from avikus_parser.iec_61162_450.udpbc import Iec61162_450_UdPbC
from avikus_parser.iec_61162_1.error import (
    Iec61162_1_Error,
    Iec61162_1_ChecksumError,
    Iec61162_1_SyntaxError,
    Iec61162_1_MessageLengthError,
    Iec61162_1_NonParsableSentenceError,
    Iec61162_1_BadNewlineEndError,
)
from avikus_parser.iec_61162_450.error import Iec61162_450_InvalidHeaderTokenError
from avikus_parser.tag.error import Tag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r, TagSyntaxError, TagFramingError
from avikus_parser.iec_61162_450._binary_image.error import (
    Iec61162_450_HeaderUnrecognized,
    Iec61162_450_DatagramMissing,
    Iec61162_450_DataBlockError,
)
from multiprocessing import Queue, Value
from utils.config import REDIS_KEY_PREFIX


# 모니터링 샘플링 상태 (스레드 안전)
_monitoring_counter = 0
_monitoring_counter_lock = threading.Lock()  # 카운터 보호
_seen_source_ids_lock = threading.Lock()  # 스레드 안전
_seen_source_ids = set()  # 로컬 캐시
_last_cleanup_time = 0
_last_cleanup_time_lock = threading.Lock()  # cleanup 시간 보호


def safe_monitoring_put(queue, data, max_size_threshold=4000, base_sampling_rate=10):
    """
    안전한 모니터링 큐 삽입 (누수 방지 + 적응형 샘플링)

    Args:
        queue: 모니터링 큐
        data: 삽입할 데이터
        max_size_threshold: 정리를 시작할 큐 크기 임계값 (기본: 4000/5000 = 80%)
        base_sampling_rate: 기본 샘플링 비율 (기본: 10 = 10개 중 1개)
    """
    global _monitoring_counter, _seen_source_ids, _last_cleanup_time

    try:
        # source_id 추출
        parsed_data = data.get("parsed_data")
        source_id = None
        if hasattr(parsed_data, "tag") and parsed_data.tag is not None:
            source_id = getattr(parsed_data.tag, "source_id", None)

        # 적응형 샘플링 로직 (스레드 안전)
        should_collect = False

        # 스레드 안전 카운터 증가
        global _monitoring_counter
        with _monitoring_counter_lock:
            _monitoring_counter += 1
            current_counter = _monitoring_counter

        if source_id:
            with _seen_source_ids_lock:
                if source_id not in _seen_source_ids:
                    # 새로운 source_id는 무조건 수집 (누락 방지)
                    should_collect = True
                    _seen_source_ids.add(source_id)
                    print(f"[MONITORING] New source_id detected: {source_id}")
                else:
                    # 기존 source_id는 샘플링 적용
                    should_collect = current_counter % base_sampling_rate == 0
        else:
            # source_id가 없는 경우 (61162-1) 더 자주 샘플링
            should_collect = current_counter % (base_sampling_rate // 2) == 0

        if not should_collect:
            return

        # 주기적으로 seen_source_ids 정리 (메모리 누수 방지, 스레드 안전)
        current_time = time.time()
        should_cleanup = False

        # cleanup 시간 체크 (스레드 안전)
        with _last_cleanup_time_lock:
            if current_time - _last_cleanup_time > 300:  # 5분마다
                _last_cleanup_time = current_time
                should_cleanup = True

        if should_cleanup:
            with _seen_source_ids_lock:
                _seen_source_ids.clear()
                print(
                    "[MONITORING] Cleared seen_source_ids cache (long-term stability)"
                )

        # 큐 크기가 임계값을 초과하면 오래된 데이터 정리
        current_size = queue.qsize()
        if current_size > max_size_threshold:
            # 1000개 정도 제거하여 여유 공간 확보
            cleanup_count = min(1000, current_size - 3000)  # 3000개까지 줄임
            for _ in range(cleanup_count):
                try:
                    queue.get_nowait()
                except:
                    break

            # 정리 로그 (너무 자주 출력되지 않도록 제한)
            if current_size > max_size_threshold + 500:  # 4500 이상일 때만 로그
                logging.warning(
                    f"[MONITORING] Queue cleanup: {current_size} -> {queue.qsize()}"
                )

        # 데이터 삽입
        queue.put_nowait(data)

    except Exception as e:
        # 큐 삽입 실패는 프로덕션에 영향 없도록 조용히 무시
        pass


# parsed_obj 을 보관할 때 편하게 쓰기 위한 구조체
ErrorLog = namedtuple("ErrorLog", ["timestamp", "msg"])

SUPPORTED_ERRORS = [
    Iec61162_1_ChecksumError,
    Iec61162_1_SyntaxError,
    Iec61162_1_MessageLengthError,
    # Iec61162_1_NonParsableSentenceError,
    Iec61162_1_BadNewlineEndError,
    Iec61162_450_InvalidHeaderTokenError,
    TagChecksumError,
    TagSyntaxError,
    TagFramingError,
    Iec61162_450_HeaderUnrecognized,
    Iec61162_450_DatagramMissing,
    Iec61162_450_DataBlockError,
]


def init_error_counter(counter_proxy, initial_last_ts: str = "-"):
    """
    counter_proxy(dict proxy)에
    Supported error 타입을 모두 0, last_ts를 '-'로 초기 세팅합니다.
    """
    for cls in SUPPORTED_ERRORS:
        name = cls.__name__
        counter_proxy[name] = 0
        counter_proxy[f"{name}__last_timestamp"] = initial_last_ts


def parse_and_save_worker(
    device_cfg,
    rules_cfg,
    redis_mgr,
    mode,
    vdm,
    raw_queue: Queue,
    parse_counter: Value,  # type: ignore
    save_counter: Value,  # type: ignore
    set_counter: Value,  # type: ignore  # SET 카운터 추가
    publish_counter: Value,  # type: ignore  # PUBLISH 카운터 추가
    log_queue=None,
    monitoring_queue=None,  # 모니터링 큐 추가
    monitoring_enabled=None,  # 모니터링 ON/OFF 플래그 추가
    pubsub_settings=None,  # PubSub 설정 추가
):
    """파싱 후 바로 Redis 저장까지 처리하는 워커"""
    cls = Parser(
        device_cfg,
        rules_cfg,
        mode,
        vdm,
        log_queue=log_queue,
        pubsub_settings=pubsub_settings,
    )

    # bring in a pipeline and batch size
    pipe = redis_mgr.get_pipeline()
    batch_size = int(os.getenv("PIPELINE_BATCH_SIZE", "500"))
    batch_count = 0

    last_flush = time.perf_counter()

    while True:
        try:
            data = raw_queue.get()
        except KeyboardInterrupt:
            logging.warning("[Parser] KeyboardInterrupt received → exiting worker")
            break

        # 0) parse
        ok, to_store_data_list = cls.parse(data)

        if not ok:
            continue

        # 2) safe_list에 대해 pipeline set
        #   - data → raw_data 와 to_store_data_list 비교로 성공 비교 판단할 수 없음
        #   - ignore, relay, 지원하지 않는 sentence는 제외된 파싱 성공 값만을 safe_list로 만들어짐
        srv, _, _ = data
        try:
            for parsed_data in to_store_data_list:
                sentence = parsed_data.sentence_name  # type: ignore
                source_id = None

                if parsed_data.tag is not None:  # type: ignore
                    source_id = parsed_data.tag.source_id  # type: ignore
                ex_time = cls.get_ttl(sentence, source_id)

                # parse 완료된 개수 만큼 증가
                with parse_counter.get_lock():
                    parse_counter.value += 1

                # 1) Redis key 계산 → JSON 직렬화 → pipeline에 enqueue
                redis_key = cls._definition_redis_key(srv, parsed_data)
                json_bytes = parsed_data.model_dump_json()  # type: ignore

                # SET 작업
                if ex_time > 0:
                    pipe.set(redis_key, json_bytes, ex=ex_time)
                else:
                    pipe.set(redis_key, json_bytes)

                # SET 카운터 증가
                with set_counter.get_lock():
                    set_counter.value += 1

                # PUBLISH 작업 (조건부)
                if cls.should_publish(srv, parsed_data):
                    pub_channel = f"{redis_key}:pub"
                    pipe.publish(pub_channel, json_bytes)
                    logging.debug(f"[PUBSUB] Queued publish to {pub_channel}")

                    # PUBLISH 카운터 증가
                    with publish_counter.get_lock():
                        publish_counter.value += 1

                    # PUBLISH 실패 시에도 SET은 정상 진행되도록 try-catch 없이 pipeline에 추가

                # ── (B) save_counter도 to_store_data_list 항목 개수만큼 증가
                batch_count += 1
                with save_counter.get_lock():
                    save_counter.value += 1

                # 모니터링 큐로 데이터 전송 (모니터링이 활성화된 경우에만)
                if (
                    monitoring_queue is not None
                    and monitoring_enabled is not None
                    and monitoring_enabled.value
                ):
                    monitoring_data = {
                        "server_config": srv,
                        "parsed_data": parsed_data,
                        "timestamp": time.time(),
                    }
                    safe_monitoring_put(monitoring_queue, monitoring_data)
        except Exception as e:
            logging.exception("[Redis save error] {e}")
            continue
        # 2) 일정 개수 이상 쌓이거나 시간이 1초 넘으면 pipeline 실행
        now = time.perf_counter()
        if batch_count >= batch_size or (now - last_flush) >= 0.5:
            start_exec = time.perf_counter()

            try:
                pipe.execute()
            except Exception as e:
                logging.error(f"[Redis pipeline error] {e}")
            duration = time.perf_counter() - start_exec

            # 만약 실행 시간이 일정 임계치(예: 0.1초)보다 길면 경고
            if duration > 0.1:
                logging.warning(
                    f"[Redis pipeline delay] execute() took {duration:.3f}s, batch_count={batch_count}"
                )

            pipe = redis_mgr.get_pipeline()
            batch_count = 0
            last_flush = now


class Parser:
    """
    • receiver에서 넘어온 raw 데이터를 tuple (ServerConfig, ProtocolVersion, raw_bytes)로 받아,
      parse() 내에서 다음 순서로 동작:
      1. apply_endpoint_rule(): 파싱 전 전체 패킷 ignore/relay 처리
      2. CRLF 검사 → NMEA 파싱 → parsed_list
      3. parsed_list 순회:
         a. apply_source_rule(): source_id 기반 ignore/relay 처리
         b. apply_sentence_rule(): sentence ID 기반 ignore/relay 처리
      4. 저장 대상이 하나라도 있으면 반환(True, 마지막 JSON)
         없으면 반환(False, "")
    """

    def __init__(
        self,
        device_config: AllDeviceSettings,
        rules: ServerRules,
        mode: Mode,
        seatrial_simulator_config=None,
        log_queue=None,
        pubsub_settings=None,
    ):
        self.device_config: AllDeviceSettings = device_config
        self.rules: ServerRules = rules
        self.selected_source_map = {}
        self.last_logged = time.time()
        # self._last_redis_key = ""
        self.prefix_redis_key = f"{REDIS_KEY_PREFIX}:" if REDIS_KEY_PREFIX else ""

        # PubSub 설정
        self.pubsub_settings = pubsub_settings
        if self.pubsub_settings and self.pubsub_settings.enabled:
            logging.info(f"[PUBSUB] Enabled with mode: {self.pubsub_settings.mode}")
            if self.pubsub_settings.mode == "filter":
                logging.info(
                    f"[PUBSUB] Filter devices: {self.pubsub_settings.filter.devices}"
                )
                logging.info(
                    f"[PUBSUB] Filter sentences: {self.pubsub_settings.filter.sentences}"
                )
        else:
            logging.info("[PUBSUB] Disabled")

        self.endpoint_rules = self.rules.endpoint_rules
        self.sentence_rule_map = {
            rule.sentence: rule for rule in self.rules.sentence_rules
        }
        self.source_rule_map = {
            rule.source_id: rule for rule in self.rules.source_rules
        }

        self.default_ttl = int(os.getenv("ADTA_EXPIRE_TIME", "10"))

        self.sentence_ttl_map = {
            rule.sentence: int(getattr(rule, "expire_time", 0))
            for rule in self.rules.sentence_rules
            if getattr(rule, "expire_time", 0) >= 0
        }

        self.source_ttl_map = {
            rule.source_id: int(rule.expire_time)
            for rule in self.rules.source_rules
            if int(getattr(rule, "expire_time", 0)) >= 0
        }

        for device_name, setting in self.device_config.devices.items():
            # e.g., "GP0001" "GPS1"
            for idx, source_id in enumerate(setting.selected_61162_450, start=1):
                self.selected_source_map[source_id] = f"{device_name}{idx}"

        self.mode = mode
        self.seatrial_simulator_config: ServerConfig | None = seatrial_simulator_config
        self.SEA_TRIAL_PORT = (
            0
            if self.seatrial_simulator_config is None
            else self.seatrial_simulator_config.port
        )

        # NMEA Parser Ready
        self.parser_61162_1 = Iec61162_1()
        self.parser_61162_450 = Iec61162_450_UdPbC("II0001")

        self.log_queue = log_queue  # CHANGED: 이벤트 큐 수신

        self._stat = {
            "recv_count": 0,
            "parse_count": 0,
            "parse_total_time": 0.0,
            "dump_total_time": 0.0,
            "last_logged": time.time(),
        }

    def parse(self, datas: tuple):  # -> tuple[bool, str]:
        """
        • datas: (srv: ServerConfig, header_version: ProtocolVersion, data: bytes)
        • 반환:
            - (False, ""): 파싱 실패, relay 발생, 또는 저장할 대상 없음
            - (True, json_str): 저장할 JSON이 하나 이상 있을 때, 송신
        """
        all_parsed_data = []
        srv, header_version, raw_data = datas
        mode = self.mode
        self._stat["recv_count"] += 1

        # ===================================================
        # TODO:: Endpoint 룰 (파싱 전) → 굳이 필요할지 모르겠음
        # should_ignore, should_relay = apply_endpoint_rule(
        #     srv=srv, raw_data=raw_data
        # )
        # if should_ignore:
        #     return False, ""
        # if should_relay:
        #     return False, ""
        # ===================================================

        # 1) ProtocolVersion에 맞춰 파싱
        try:
            t1 = time.perf_counter()
            if header_version == ProtocolVersion.V450:
                parsed_list = self.parser_61162_450.parse(raw_data)
            elif header_version == ProtocolVersion.V1:
                parsed_list = self.parser_61162_1.parse(raw_data)
            else:
                logging.warning(
                    f"[Parser] 지원하지 않는 프로토콜 버전: {header_version}"
                )
                return False, ""
            self._stat["parse_total_time"] += time.perf_counter() - t1
        except Exception as e:
            logging.error(f"[Avikus Parser Error] : {e} {raw_data}")
            return False, ""

        # 2) parsed_list 순회하며 Source/문장 룰 적용 → JSON 저장 대상 준비
        raw_lines = raw_data.split(b"\r\n")

        for raw_line, parsed in zip(raw_lines, parsed_list):
            # 문장 이름 추출 (예: "$TTM" → "TTM")
            sentence_name = getattr(parsed, "sentence", parsed.__class__.__name__)
            parsed.sentence_name = sentence_name  # type: ignore
            parsed.issued_time = datetime.utcnow().isoformat()  # type: ignore

            # 파싱 에러(skip) (Iec61162_1_Error)
            if self._parsed_error(parsed):
                continue

            # 3-a) Sentence 룰
            # SEA TRIAL 용 조건문
            if (
                mode is Mode.SEA_TRIAL
                and sentence_name in {"VDM", "TTD", "TTM"}
                and srv.port != self.SEA_TRIAL_PORT
            ):
                continue

            sent_ignore, sent_relay = apply_sentence_rule(
                sentence_rule_map=self.sentence_rule_map,
                parsed_obj=parsed,
                raw_line=raw_line,
            )
            ## ignore, relay 각각 옵션 동작하도록 수정
            if sent_ignore:
                continue

            # 3-b) 저장 대상 parsed 처리
            try:
                t2 = time.perf_counter()

                if isinstance(parsed, Exception):
                    logging.error(
                        f"[Parsed object is an Exception]: {raw_line} {parsed}"
                    )
                    continue
                self._stat["dump_total_time"] += time.perf_counter() - t2

                # Redis 에 적용할 Key 가공

                # 3-c) Source 룰
                s_ignore, s_relay = apply_source_rule(
                    srv=srv,
                    source_rule_map=self.source_rule_map,
                    parsed=parsed,
                    raw_line=raw_line,
                )
                ## ignore, relay 각각 옵션 동작하도록 수정
                if s_ignore:
                    continue

                all_parsed_data.append(parsed)
                self._stat["parse_count"] += 1

            except Exception as e:
                logging.error(f"[Field extraction error] {e}, data: {parsed}")
                continue

        now = time.time()
        if now - self._stat["last_logged"] >= 1.0:
            recv = self._stat["recv_count"]
            parsed = self._stat["parse_count"]
            avg_parse = (
                (self._stat["parse_total_time"] / parsed * 1000) if parsed else 0
            )
            avg_dump = (self._stat["dump_total_time"] / parsed * 1000) if parsed else 0

            logging.debug(
                f"[STAT] recv={recv}/s, parsed={parsed}/s, "
                f"avg_parse={avg_parse:.2f}ms, avg_dump={avg_dump:.2f}ms"
            )

            self._stat.update(
                {
                    "recv_count": 0,
                    "parse_count": 0,
                    "parse_total_time": 0.0,
                    "dump_total_time": 0.0,
                    "last_logged": now,
                }
            )

        # 4) 저장할 데이터가 하나라도 있으면, 송신
        if all_parsed_data:
            return True, all_parsed_data
        else:
            return False, []

    def _definition_redis_key(self, srv, parsed_data):
        talker_name = parsed_data.talker
        sentence_name = parsed_data.sentence_name
        data_key = f"{talker_name}{sentence_name}"
        local_device_name = srv.name

        # 61162-1 Sentence 타입의 메시지는 tag 가 없음
        ## TO DO: sentence mapping 에 따른 기능 추가
        ## ex) 6601 + GPGGA는 -> GPS1:GPGGA
        ## ex) 6602 + GPGGA는 -> GPS2:GPGGA
        if parsed_data.tag is None:

            return f"{self.prefix_redis_key}{local_device_name.lower()}:{data_key}"

        # 61162-450 파서의 경우 tag 가 있어서 source_id로 redis key 발행
        else:
            source_id = parsed_data.tag.source_id

            ## 마린전자 대응 조건문 :: TAG가 있음에도 source_id 가 None일 수 있다.
            if source_id is None:
                return f"{self.prefix_redis_key}{local_device_name.lower()}:{data_key}"

            device_name = self.selected_source_map.get(source_id, "UNKNOWN450")
            return f"{self.prefix_redis_key}{device_name.lower()}:{data_key}"

    def get_ttl(self, sentence, source_id=None):
        # 1) 기본
        ex_time = self.sentence_ttl_map.get(sentence, self.default_ttl)

        # 2) 소스 룰이 있으면 우선 적용
        if source_id and source_id in self.source_ttl_map:
            ex_time = self.source_ttl_map[source_id]

        return ex_time

    def should_publish(self, srv, parsed_data):
        """PubSub 발행 여부 결정"""
        # PubSub 비활성화 시
        if not self.pubsub_settings or not self.pubsub_settings.enabled:
            return False

        # "all" 모드: 모든 데이터 발행
        if self.pubsub_settings.mode == "all":
            return True

        # "filter" 모드: 조건 확인
        if self.pubsub_settings.mode == "filter":
            # 디바이스 필터 확인
            device_name = srv.name
            if (
                self.pubsub_settings.filter.devices
                and device_name not in self.pubsub_settings.filter.devices
            ):
                return False

            # 문장 필터 확인 (sentence_name만 사용, 예: VDM)
            sentence_name = parsed_data.sentence_name
            if (
                self.pubsub_settings.filter.sentences
                and sentence_name not in self.pubsub_settings.filter.sentences
            ):
                return False

            return True

        # 알 수 없는 모드
        logging.warning(f"[PUBSUB] Unknown mode: {self.pubsub_settings.mode}")
        return False

    def _parsed_error(self, parsed_obj):
        # IEC 61162-1 에러가 아니라면 패스
        if not isinstance(parsed_obj, Iec61162_1_Error):
            return False

        etype = type(parsed_obj).__name__
        timestamp = int(time.time())

        # parsed_obj에서 직접 속성 가져오기
        readable_name = getattr(parsed_obj, "readable_name", etype)
        is_mandatory = getattr(parsed_obj, "is_mandatory_error", True)

        # 메시지 생성 (readable_name 사용)
        msg = f"[{readable_name}]: {str(parsed_obj)}"

        # CHANGED: non-blocking 큐에 이벤트 전송, 실패 시 무시
        # is_mandatory가 False인 경우 로그 전송하지 않음
        if is_mandatory:
            try:
                self.log_queue.put_nowait((etype, timestamp, msg))  # type: ignore
            except queue.Full:
                # logging.warning(f"[Error Logging] queue full, dropping {etype}")
                pass
            except Exception as e:
                logging.exception(f"[Error Logging] unexpected: {e}")

        # 에러 클래스별로 로그 분기
        if isinstance(parsed_obj, Iec61162_1_NonParsableSentenceError):
            logging.debug(f"[Warn] [Non Support]: {parsed_obj}")

        #  문장 내 문법 에러
        elif isinstance(parsed_obj, Iec61162_1_SyntaxError):
            logging.warning(f"[Warn] [Syntax Error]: {parsed_obj}")

        # 체크섬 에러
        elif isinstance(parsed_obj, Iec61162_1_ChecksumError):
            logging.debug(f"[Warn] [Checksum Error]: {parsed_obj}")

        # 메시지 최대 길이 초과 에러
        elif isinstance(parsed_obj, Iec61162_1_MessageLengthError):
            logging.info(f"[Warn] [Length Error]: {parsed_obj}")

        # 헤더 토큰(UdPbC, etc.) 에러
        elif isinstance(parsed_obj, Iec61162_450_InvalidHeaderTokenError):
            logging.info(f"[Warn] [Invalid Header Error]: {parsed_obj}")

        # [IEC 61162-450] TAG 블록 체크섬 에러
        elif isinstance(parsed_obj, TagChecksumError):
            logging.info(f"[Warn] [Tag Chgecksum Error]: {parsed_obj}")

        # [IEC 61162-450] TAG 블록 내 문법 에러
        elif isinstance(parsed_obj, TagSyntaxError):
            logging.info(f"[Warn] [Tag Syntax Error]: {parsed_obj}")

        #  [IEC 61162-450] TAG 블록의 형식(framing) 에러
        elif isinstance(parsed_obj, TagFramingError):
            logging.info(f"[Error] [Tag Framing]: {parsed_obj}")

        # [IEC 61162-450] 이진 파일 헤더 에러
        elif isinstance(parsed_obj, Iec61162_450_HeaderUnrecognized):
            logging.info(f"[Error] [Binary Header Unrecognized]: {parsed_obj}")

        # [IEC 61162-450] 이진 파일 데이터그램 미수신 에러
        elif isinstance(parsed_obj, Iec61162_450_DatagramMissing):
            logging.info(f"[Error] [Datagram Missing]: {parsed_obj}")

        # [IEC 61162-450] 이진 파일 데이터 그램 내 에러
        elif isinstance(parsed_obj, Iec61162_450_DataBlockError):
            logging.info(f"[Error] [Data Block]: {parsed_obj}")

        elif isinstance(parsed_obj, Iec61162_1_BadNewlineEndError):
            logging.info(f"[Error] [Bad NewLine]: {parsed_obj}")

        else:
            # 그 외의 Iec61162_1_Error (예: RenderError 등)
            logging.warning(f"[Warn] {parsed_obj}")

        return True
