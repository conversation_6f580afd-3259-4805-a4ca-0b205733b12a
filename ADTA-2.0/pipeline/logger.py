#### pipeline/logger.py

import logging
import queue
import time
import json
import os
import threading
from collections import Counter, deque
from utils.config import (
    ERROR_COUNTER_MAX,
    ERROR_LOG_MAX,
    ERROR_LOG_FLUSH_INTERVAL,  # NEW: flush 주기 (초)
    ERROR_COUNTER_FLUSH_THRESHOLD,  # NEW: proxy로 보낼 최소 이벤트 수
)


class ErrorLogger:
    """
    이벤트를 로컬 버퍼(Counter, deque)에 모았다가
    주기(flush_interval)나 개수(flush_threshold) 도달 시
    한 번에 shared proxy에 적용해 IPC 오버헤드를 최소화합니다.
    """

    def __init__(
        self,
        event_queue,
        counter_proxy,  # Manager.dict
        logs_proxy,  # Manager.list
        max_count=ERROR_COUNTER_MAX,
        max_logs=ERROR_LOG_MAX,
        flush_interval=ERROR_LOG_FLUSH_INTERVAL,
        flush_threshold=ERROR_COUNTER_FLUSH_THRESHOLD,
    ):
        self.queue = event_queue
        self.counter_proxy = counter_proxy
        self.logs_proxy = logs_proxy
        self.max_count = max_count
        self.max_logs = max_logs
        self.flush_interval = flush_interval
        self.flush_threshold = flush_threshold

        # CHANGED: 로컬 버퍼
        self.local_counts = Counter()
        self.local_last_ts = {}
        self.local_logs = deque(maxlen=self.max_logs)

        self.last_flush = time.time()

        # 스냅샷 관련 설정
        self.snapshot_file = "config/errors/error_snapshot.json"
        self.last_snapshot = time.time()
        self.snapshot_interval = 30  # 30초마다 스냅샷

        # 스냅샷 디렉토리 생성
        os.makedirs(os.path.dirname(self.snapshot_file), exist_ok=True)

    def run(self):
        while True:
            try:
                etype, ts, msg = self.queue.get()  # 블로킹
            except Exception as e:
                logging.exception(f"[ErrorLogger] queue.get failed: {e}")
                continue

            # CHANGED: 로컬 Counter/last_ts 업데이트
            if self.local_counts[etype] < self.max_count:
                self.local_counts[etype] += 1
                self.local_last_ts[etype] = ts

            # CHANGED: 로컬 로그 버퍼에 추가
            self.local_logs.append((ts, msg))

            # CHANGED: flush 조건 검사
            now = time.time()
            if (
                sum(self.local_counts.values()) >= self.flush_threshold
                or now - self.last_flush >= self.flush_interval
            ):
                self._flush()

    def _flush(self):
        """
        로컬 버퍼에 모인 이벤트를 한 번에 shared proxy에 반영
        """
        # 1) Counter proxy batch update
        for etype, cnt in self.local_counts.items():
            prev = self.counter_proxy.get(etype, 0)
            add = min(cnt, self.max_count - prev)
            if add > 0:
                self.counter_proxy[etype] = prev + add
                self.counter_proxy[f"{etype}__last_timestamp"] = self.local_last_ts[
                    etype
                ]

        # 2) Logs proxy batch update (메모리 누수 방지 강화)
        # 추가하기 전에 미리 공간 확보
        space_needed = len(self.local_logs)
        current_size = len(self.logs_proxy)

        # 추가 후 최대 크기를 초과할 경우 미리 정리
        if current_size + space_needed > self.max_logs:
            excess = current_size + space_needed - self.max_logs
            # 안전한 범위에서 제거 (인덱스 오류 방지)
            remove_count = min(excess, current_size)
            for _ in range(remove_count):
                if len(self.logs_proxy) > 0:
                    self.logs_proxy.pop(0)

        # 로그 추가
        for ts, msg in self.local_logs:
            self.logs_proxy.append((ts, msg))

        # 3) 최종 크기 검증 및 강제 제한 (이중 안전장치)
        while len(self.logs_proxy) > self.max_logs:
            self.logs_proxy.pop(0)

        # 4) 메모리 사용량 모니터링 (주기적 로깅)
        if hasattr(self, "_last_memory_log"):
            if time.time() - self._last_memory_log > 300:  # 5분마다
                logging.info(
                    f"[ErrorLogger] Memory status: {len(self.logs_proxy)}/{self.max_logs} logs"
                )
                self._last_memory_log = time.time()
        else:
            self._last_memory_log = time.time()

        # 5) 로컬 버퍼 초기화
        self.local_counts.clear()
        self.local_last_ts.clear()
        self.local_logs.clear()
        self.last_flush = time.time()

        # 6) 주기적 스냅샷 저장 (30초마다, 비동기)
        now = time.time()
        if now - self.last_snapshot > self.snapshot_interval:
            # 별도 스레드에서 비동기 저장 (I/O 블로킹 방지)
            threading.Thread(target=self._save_snapshot, daemon=True).start()
            self.last_snapshot = now

    def _save_snapshot(self):
        """현재 상태를 스냅샷 파일에 저장 (원자적 쓰기)"""
        try:
            # 현재 상태 수집 (단순 보존)
            snapshot = {
                "version": "1.1",  # 단순화 버전
                "created_at": int(time.time()),  # 생성 시간
                "counter": dict(self.counter_proxy),
                "logs": list(self.logs_proxy)[-100:],  # 최근 100개만 보존
            }

            # 원자적 쓰기 (임시 파일 → 이동)
            temp_file = self.snapshot_file + ".tmp"
            with open(temp_file, "w") as f:
                json.dump(snapshot, f, indent=2)

            # 원자적 이동
            os.rename(temp_file, self.snapshot_file)

            logging.debug(
                f"[ErrorLogger] Snapshot saved: {len(snapshot['counter'])} counters, {len(snapshot['logs'])} logs"
            )

        except Exception as e:
            logging.warning(f"[ErrorLogger] Failed to save snapshot: {e}")
            # 임시 파일 정리
            try:
                temp_file = self.snapshot_file + ".tmp"
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass
