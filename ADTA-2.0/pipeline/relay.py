# 파일: pipeline/relay.py
import socket
import logging
from contextlib import suppress

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] %(levelname)s - %(message)s",
)


class Relayer:
    def __init__(self):
        # 모듈 로드 시 단 한 번 소켓 생성
        self._sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self._sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 16 * 1024 * 1024)
        self._sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        logging.info("[Relay] Initialized socket (SO_SNDBUF=16MB, REUSEADDR=1)")

    def relay(self, host: str, port: int, data: bytes) -> bool:
        try:
            self._sock.sendto(data, (host, port))
            logging.debug(f"[Relay] {host}:{port} ← {len(data)} bytes")
            return True
        except Exception:
            logging.exception(f"[Relay 실패] {host}:{port}")
            return False

    def close(self):
        with suppress(Exception):
            self._sock.close()


# 전역 인스턴스 (import 만으로 재사용)
relay = Relayer()
logging.info("[Relay] Global relay instance created and ready to use.")
