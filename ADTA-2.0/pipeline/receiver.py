# 파일명: receiver.py

import socket
import struct
import select
import logging
from multiprocessing import Queue, Value

from model.server import ServerConfig

from utils.config import ADTA_HINAS_IP
from utils.socket_helpers import _is_multicast_ip

BUFFER_SIZE = 2048
SELECT_TIMEOUT = 1.0

from model.sensor import Mode


def receive_worker(
    mode: Mode,
    simulator_config: dict,
    servers: list[ServerConfig],
    raw_queue: Queue,
    recv_counter: Value,  # type: ignore
):
    """
    • servers 리스트 순회: transport에 따라 UDP 소켓 또는 TCP 리스너 생성
      - UDP인 경우, srv.interface에 적힌 IP가 멀티캐스트인지 유니캐스트인지 자동 판단
    • select() 루프:
        - UDP 소켓: recvfrom() → raw_queue.put((srv, srv.message_protocol, data)) → recv_counter++
        - TCP 리스너: accept() → 논블로킹 conn 추가
        - TCP conn: recv() → raw_queue.put((srv, srv.message_protocol, data)) → recv_counter++, 빈 바이트면 close()
    """
    udp_socks = []  # List[ (socket, ServerConfig) ]
    tcp_listeners = []  # List[ (socket, ServerConfig) ]
    tcp_conns = []  # List[ (socket, ServerConfig) ]

    # 0) mode
    if mode == Mode.SEA_TRIAL:
        if simulator_config.transport_protocol.lower() == "tcp":  # type: ignore
            logging.warning(
                "SEA_TRIAL 모드에서는 TCP 전송 프로토콜을 사용할 수 없습니다."
            )
        else:
            udp_sock = _create_udp_socket(simulator_config)  # type: ignore
            udp_socks.append((udp_sock, simulator_config))

    # 1) 소켓 생성
    for srv in servers:
        if srv.transport_protocol.lower() == "tcp":
            listener = _create_tcp_listener(srv)
            tcp_listeners.append((listener, srv))
        else:
            udp_sock = _create_udp_socket(srv)
            udp_socks.append((udp_sock, srv))

    # 2) select() 입력 목록 준비
    inputs = [sock for sock, _ in udp_socks] + [sock for sock, _ in tcp_listeners]

    # 3) 무한 루프
    while True:
        try:
            ready, _, _ = select.select(inputs, [], [], SELECT_TIMEOUT)
        except KeyboardInterrupt:
            logging.warning("[Receiver] KeyboardInterrupt received → exiting worker")
            break

        for s in ready:
            # --- UDP 처리 ---
            for udp_sock, srv in udp_socks:
                if s is udp_sock:
                    try:
                        data, _addr = udp_sock.recvfrom(BUFFER_SIZE)
                    except Exception:
                        logging.exception(
                            f"[UDP] recvfrom 실패: {srv.address}:{srv.port}"
                        )
                        break
                    # 이제 ServerConfig 객체 전체를 넘김
                    raw_queue.put((srv, srv.message_protocol, data))

                    # Raw 패킷 스트림 모니터링 데이터 수집
                    try:
                        from api.routers.raw_stream_monitor import (
                            add_raw_packet_for_stream,
                        )

                        # 멀티캐스트 destination 정보 생성
                        destination = f"{srv.address}:{srv.port}"
                        add_raw_packet_for_stream(srv.name, srv.port, destination, data)

                        # 첫 번째 호출 시 로그 (디버깅용)
                        if not hasattr(
                            add_raw_packet_for_stream, "_receiver_debug_logged"
                        ):
                            logging.info(
                                f"[Receiver] Raw stream monitor called: {srv.name}:{srv.port}"
                            )
                            add_raw_packet_for_stream._receiver_debug_logged = True

                    except Exception as e:
                        # 첫 번째 오류만 로그
                        if not hasattr(
                            add_raw_packet_for_stream, "_receiver_error_logged"
                        ):
                            logging.error(f"[Receiver] Raw stream monitor error: {e}")
                            add_raw_packet_for_stream._receiver_error_logged = True

                    with recv_counter.get_lock():
                        recv_counter.value += 1
                    break

            else:
                # --- TCP 리스너 처리 ---
                for listener, srv in tcp_listeners:
                    if s is listener:
                        try:
                            conn, _ = listener.accept()
                            conn.setblocking(False)
                        except Exception:
                            logging.exception(
                                f"[TCP] accept 실패: {srv.address}:{srv.port}"
                            )
                            break
                        tcp_conns.append((conn, srv))
                        inputs.append(conn)
                        break

                else:
                    # --- 기존 TCP 연결 처리 ---
                    for conn, srv in list(tcp_conns):
                        if s is conn:
                            try:
                                data = conn.recv(BUFFER_SIZE)
                            except Exception:
                                data = b""
                            if not data:
                                inputs.remove(conn)
                                conn.close()
                                tcp_conns.remove((conn, srv))
                            else:
                                raw_queue.put((srv, srv.message_protocol, data))

                                # Raw 패킷 스트림 모니터링 데이터 수집 (TCP)
                                try:
                                    from api.routers.raw_stream_monitor import (
                                        add_raw_packet_for_stream,
                                    )

                                    # TCP destination 정보 생성
                                    destination = f"{srv.address}:{srv.port}"
                                    add_raw_packet_for_stream(
                                        srv.name, srv.port, destination, data
                                    )

                                    # 첫 번째 호출 시 로그 (디버깅용)
                                    if not hasattr(
                                        add_raw_packet_for_stream,
                                        "_receiver_tcp_debug_logged",
                                    ):
                                        logging.info(
                                            f"[Receiver-TCP] Raw stream monitor called: {srv.name}:{srv.port}"
                                        )
                                        add_raw_packet_for_stream._receiver_tcp_debug_logged = (
                                            True
                                        )

                                except Exception as e:
                                    # 첫 번째 오류만 로그
                                    if not hasattr(
                                        add_raw_packet_for_stream,
                                        "_receiver_tcp_error_logged",
                                    ):
                                        logging.error(
                                            f"[Receiver-TCP] Raw stream monitor error: {e}"
                                        )
                                        add_raw_packet_for_stream._receiver_tcp_error_logged = (
                                            True
                                        )

                                with recv_counter.get_lock():
                                    recv_counter.value += 1
                            break


# ------------------- helper functions -------------------


# def _get_interface_name(ip_address: str):  # -> Optional[str]:
#     """
#     주어진 IPv4 주소가 할당된 NIC 이름을 반환.
#     psutil 네트워크 인터페이스 정보를 사용해 깔끔하게 구현.
#     """
#     for iface, addrs in psutil.net_if_addrs().items():
#         for addr in addrs:
#             if addr.family == socket.AF_INET and addr.address == ip_address:
#                 return iface
#     return None


def _join_multicast_group(raw_socket: socket.socket, mcast_group: str):
    # 1) 환경 변수에서 내 IP를 가져온다
    hinas_ip = ADTA_HINAS_IP
    try:
        iface_bytes = socket.inet_aton(hinas_ip)
    except OSError:
        logging.warning(f"Invalid ADTA_HINAS_IP '{hinas_ip}', using 0.0.0.0")
        iface_bytes = socket.inet_aton("0.0.0.0")

    mreq = struct.pack("4s4s", socket.inet_aton(mcast_group), iface_bytes)
    raw_socket.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)


def _create_udp_socket(srv: ServerConfig):  # -> socket.socket:
    """
    • srv.address 지정된 IP만 보고 멀티캐스트/유니캐스트 자동 판단
    • 멀티캐스트: ("", port)에 바인드 → IP_ADD_MEMBERSHIP(INADDR_ANY)
    • 유니캐스트: (srv.address, port)에 바인드
    • 마지막 옥텟 255면 브로드캐스트로 간주하여 SO_BROADCAST 설정
    """
    interface_ip = srv.address

    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEPORT, 1)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 16 * 1024 * 1024)

    if _is_multicast_ip(interface_ip):
        sock.bind(("", srv.port))
        try:
            _join_multicast_group(raw_socket=sock, mcast_group=interface_ip)
        except Exception as e:
            raise RuntimeError(
                f"[Multi] {interface_ip}:{srv.port} 가입 실패 (address=ALL): {e}"
            )
    else:
        try:
            sock.bind((interface_ip, srv.port))
        except Exception as e:
            raise RuntimeError(f"[UDP] {interface_ip}:{srv.port} bind failed: {e}")
        last_octet = int(interface_ip.split(".")[-1])
        if last_octet == 255:
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)

    return sock


def _create_tcp_listener(srv: ServerConfig) -> socket.socket:
    """
    • (srv.address, srv.port)에 바인드 후 listen(), 논블로킹 모드 설정
    """
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEPORT, 1)
    try:
        sock.bind((srv.address, srv.port))
    except Exception as e:
        raise RuntimeError(f"[TCP]  {srv.address}:{srv.port} bind failed: {e}")
    sock.listen(100)
    sock.setblocking(False)
    return sock
