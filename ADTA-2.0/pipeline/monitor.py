import time
import logging
from multiprocessing import Value, Queue, Event
from collections import deque
from dataclasses import dataclass


@dataclass(frozen=True)
class MonitorConfig:
    recv_ctr: Value  # type: ignore
    parse_ctr: Value  # type: ignore
    save_ctr: Value  # type: ignore
    metrics: dict
    raw_queue: Queue
    log_queue: Queue
    stop_event: Event  # type: ignore


def monitor_worker(cfg: MonitorConfig):
    """
    Sliding-window monitor for recv/parse/save rates (10s average).
    Graceful shutdown via cfg.stop_event.
    """
    recv_win = deque(maxlen=10)
    parse_win = deque(maxlen=10)
    save_win = deque(maxlen=10)

    while not cfg.stop_event.is_set():
        try:
            time.sleep(1)
        except Exception as e:
            logging.exception("sleep failed")
            continue

        try:
            with cfg.recv_ctr.get_lock():
                r = cfg.recv_ctr.value
                cfg.recv_ctr.value = 0
            with cfg.parse_ctr.get_lock():
                p = cfg.parse_ctr.value
                cfg.parse_ctr.value = 0
            with cfg.save_ctr.get_lock():
                s = cfg.save_ctr.value
                cfg.save_ctr.value = 0

            # 윈도우 업데이트
            recv_win.append(r)
            parse_win.append(p)
            save_win.append(s)

            # 윈도우 길이 (초 단위)
            n_r = len(recv_win)
            n_p = len(parse_win)
            n_s = len(save_win)

            # 최근 n초 평균 계산
            avg_r = sum(recv_win) / n_r if n_r else 0.0
            avg_p = sum(parse_win) / n_p if n_p else 0.0
            avg_s = sum(save_win) / n_s if n_s else 0.0

            # metrics 업데이트
            cfg.metrics["recv_dps_10s_avg"] = avg_r
            cfg.metrics["parse_sps_10s_avg"] = avg_p
            cfg.metrics["save_sps_10s_avg"] = avg_s

            # backlog 조회
            try:
                b = cfg.raw_queue.qsize()
            except Exception:
                b = -1
            try:
                e = cfg.log_queue.qsize()
            except Exception:
                e = -1
            cfg.metrics["raw_backlog"] = b
            cfg.metrics["error_log_queue"] = e

            # **간결한 INFO 로그**
            # [recv DPS, backlog NMEA SPS, parse NMEA SPS, save NMEA SPS] 10s-avg=[r,p,s]
            # - DPS: Datagram Per Second
            # - SPS: Sentence Per Second
            logging.info(
                f"[Metrics] "
                f"[recv(dps)={r}, backlog(Q)={b}, parse(sps)={p}, save(sps)={s}, error(Q)={e}] "
                f"10s-avg=[{avg_r:.1f}, {avg_p:.1f}, {avg_s:.1f}]"
            )

        except Exception:
            logging.exception("monitor loop failed")

    logging.info("Monitor worker stopping")
