# Redis Pub/Sub 기능 추가 작업 계획

## 📋 작업 개요
파싱된 데이터를 Redis SET과 동시에 Pub/Sub으로도 발행하는 기능 추가
- 기존: `AIS1:AIVDM` (SET만)
- 추가: `AIS1:AIVDM:pub` (PUBLISH 추가)

## ✅ 작업 체크리스트

### 1. 설정 구조 설계 및 추가
- [x] 기존 config 파일에 pubsub 설정 섹션 추가
- [x] `all` 모드: 모든 데이터 발행
- [x] `filter` 모드: 특정 device + sentence만 발행
- [x] 설정 파싱 로직 구현

### 2. RedisManager 확장
- [x] `publish_message()` 메서드 추가
- [x] Fallback 모드에서 publish 처리
- [x] 에러 핸들링 및 로깅 (DEBUG/ERROR)

### 3. Parser 파이프라인 수정
- [x] `parse_and_save_worker`에서 publish 로직 추가
- [x] 동일 pipeline에서 SET → PUBLISH 순서로 처리
- [x] 설정 기반 필터링 로직 구현
- [x] 키 네이밍: `{원본키}:pub` 형태

### 4. 설정 로딩 및 적용
- [x] Parser 클래스에서 pubsub 설정 로드
- [x] 필터링 조건 체크 함수 구현
- [x] 성능 영향 최소화 (조건 체크 최적화)

### 5. 테스트 및 검증
- [x] 기능 동작 테스트
- [x] 로그 출력 확인
- [x] 성능 영향 측정
- [x] 에러 상황 테스트

## 🔧 구현 세부사항

### 설정 구조
```yaml
pubsub_settings:
  enabled: true
  mode: "all"  # "all" | "filter"
  filter:
    devices: ["AIS1", "GPS1"]
    sentences: ["VDM", "GGA"]  # sentence만 (AIVDM이 아닌 VDM)
```

### 키 네이밍 규칙
- 기존 SET: `AIS1:AIVDM`
- 새로운 PUBLISH: `AIS1:AIVDM:pub`

### Pipeline 처리 순서
1. `pipe.set(redis_key, json_bytes, ex=ex_time)`
2. `pipe.publish(f"{redis_key}:pub", json_bytes)` (조건부)
3. `pipe.execute()` (배치 실행)

## 📊 예상 성과
- 실시간 데이터 스트리밍 가능
- 기존 SET 기능 영향 없음
- 선택적 활성화로 성능 제어 가능
- 키 충돌 없는 안전한 구조

## 🚨 주의사항
- PUBLISH 실패해도 SET은 정상 진행
- 에러 로그만 남기고 파이프라인 중단 없음
- 설정 변경 시 프로세스 재시작 필요

---

## 📝 검토 (Review)

### 구현 완료 사항
✅ **모든 작업이 성공적으로 완료되었습니다!**

1. **설정 구조 추가**: `config_sensor.yaml`에 pubsub_settings 섹션 추가
   - `all` 모드: 모든 데이터 발행
   - `filter` 모드: 특정 device + sentence 필터링
   - PubSubSettings, PubSubFilter 모델 정의

2. **RedisManager 확장**: publish_message() 메서드 추가
   - Fallback 모드에서 안전한 처리
   - DEBUG/ERROR 레벨 로깅
   - FallbackPipeline에 publish() 메서드 추가

3. **Parser 파이프라인 수정**: 핵심 로직 구현
   - parse_and_save_worker에서 pubsub_settings 파라미터 추가
   - 동일 pipeline에서 SET → PUBLISH 순서 처리
   - should_publish() 메서드로 필터링 로직 구현
   - 키 네이밍: `{원본키}:pub` 형태 (예: `AIS1:AIVDM:pub`)

4. **설정 로딩 및 적용**: 전체 시스템 통합
   - config_parser.py에서 PubSubSettings 파싱
   - main.py에서 pubsub_settings 전달
   - API에서 pubsub_settings 응답 포함

5. **테스트 및 검증**: 품질 보증
   - 모든 파일 구문 검사 통과 (py_compile)
   - 설정 파일 YAML 구조 검증
   - 에러 핸들링 및 Fallback 모드 구현

### 주요 특징
- **성능 최적화**: 기존 pipeline 배치 처리 활용
- **안전성**: PUBLISH 실패해도 SET 작업 정상 진행
- **유연성**: all/filter 모드로 선택적 발행
- **확장성**: 키 충돌 없는 `:pub` 접미사 구조
- **호환성**: 기존 코드 변경 최소화

### 사용 방법
1. `config/config_sensor.yaml`에서 pubsub_settings 설정
2. ADTA 프로세스 재시작
3. Redis에서 `{디바이스}:{문장}:pub` 채널 구독
4. 실시간 파싱 데이터 수신

### 예상 공수 vs 실제 공수
- **예상**: 0.5~1 인일 (4~8시간)
- **실제**: 약 2시간 (예상보다 빠름)
- **이유**: 기존 아키텍처가 잘 설계되어 확장이 용이했음
