FROM 479435310497.dkr.ecr.ap-northeast-2.amazonaws.com/library:python-3.12-slim-cve-patched

# non-root 사용자 생성
RUN useradd -ms /bin/bash -s /usr/sbin/nologin avikus

WORKDIR /workspace
COPY requirements.txt /workspace/
RUN pip install --no-cache-dir --upgrade pip &&\
    pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org pause && \
    pip install --no-cache-dir -r requirements.txt
COPY . /workspace

RUN chown -R avikus:avikus /workspace && \
    chmod -R 555 /workspace

USER avikus

CMD ["python3", "main.py"]