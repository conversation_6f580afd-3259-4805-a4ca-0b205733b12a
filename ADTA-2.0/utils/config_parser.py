import logging
from pydantic import ValidationError
import yaml
from typing import Any, Dict

from model.sensor import Mode
from model.server import ServerConfig, ServerRules, PubSubSettings
from model.device import AllDeviceSettings  # 모델 정의된 곳


def load_yaml_config(file_path: str):  # -> dict:
    try:
        with open(file_path, encoding="utf-8") as file:
            config = yaml.safe_load(file)
        return config
    except FileNotFoundError as e:
        raise FileNotFoundError(f"Configuration file '{file_path}' not found.") from e
    except yaml.YAMLError as e:
        raise ValueError(f"Error parsing YAML file: {e}") from e


def get_device_config(config_path: str):
    """
    Load and validate device source configuration from YAML.

    Args:
        config_path (str): Path to the YAML file.

    Returns:
        AllDeviceSettings: Validated device source configuration.
    """
    raw = load_yaml_config(config_path)

    try:
        config = AllDeviceSettings.parse_obj(raw)
        logging.debug(f"[device-config] Loaded devices: {list(config.devices.keys())}")
        return config
    except ValidationError as e:
        logging.error(f"[device-config] Validation failed: {e}")
        raise
    except Exception as e:
        logging.error(f"[device-config] Unexpected error: {e}")
        raise


def get_config(
    config_path,
):  # -> tuple[list[ServerConfig], ServerRules, Mode, ServerConfig | None, PubSubSettings]:
    """
    Get the configuration for the sensors.

    Returns:
        tuple: servers, rules, mode, vdm_simulator_config, pubsub_settings
    """
    config = load_yaml_config(config_path)
    try:
        servers: list[ServerConfig] = parse_server_config(config["server"])
    except KeyError as e:
        raise KeyError("No 'server' section found in the configuration file.") from e

    try:
        rules: ServerRules = ServerRules(**config["rules"])
    except KeyError as e:
        raise KeyError("No 'rules' section found in the configuration file.") from e

    # PubSub 설정 파싱 (선택적)
    try:
        pubsub_settings: PubSubSettings = PubSubSettings(
            **config.get("pubsub_settings", {})
        )
        logging.debug(
            f"[config] PubSub settings loaded: enabled={pubsub_settings.enabled}, mode={pubsub_settings.mode}"
        )
    except Exception as e:
        logging.warning(
            f"[config] Failed to parse pubsub_settings, using defaults: {e}"
        )
        pubsub_settings = PubSubSettings()

    try:
        mode = parse_mode_config(config.get("mode", Mode.PRODUCTION))
        if mode == Mode.SEA_TRIAL:
            try:
                vdm_simulator_config: ServerConfig = parse_seatrial_simulator_config(
                    config["seatrial_simulator_mode"]
                )
            except KeyError as e:
                raise ValueError(
                    "'seatrial_simulator_mode' is required in sea trial mode"
                ) from e
        else:
            vdm_simulator_config = None  # type: ignore
    except KeyError as e:
        raise KeyError("No 'mode' section found in the configuration file.") from e
    return servers, rules, mode, vdm_simulator_config, pubsub_settings


def parse_mode_config(config: str):  # -> Mode:
    """
    Parses a dictionary of mode configurations and returns a Mode object.

    Args:
        config (str): A string representing the mode configuration. seatrial or production.

    Returns:
        Mode: An object containing the mode configurations.

    Raises:
        ValidationError: If there is an error validating the mode data.
    """
    try:
        mode = Mode(config)
    except Exception as e:
        logging.error(f"Failed to load parse mode configuration: {e}")
    logging.info(f"[CONFIG] Running in {mode} mode")
    return mode


def parse_server_config(config: list[dict]):  # -> list[ServerConfig]:
    try:
        server_config = [ServerConfig(**conf) for conf in config]
    except Exception as e:
        logging.error(f"Failed to load server configuration: {e}")
    return server_config


def parse_seatrial_simulator_config(config: dict):  # -> ServerConfig:
    try:
        vdm_simulator_server = ServerConfig(**config)
    except Exception as e:
        logging.error(f"Failed to load vdm simulator configuration: {e}")
    return vdm_simulator_server
