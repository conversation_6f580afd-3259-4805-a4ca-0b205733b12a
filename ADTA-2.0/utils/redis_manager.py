#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import redis
import datetime
import json
import logging
import time
import threading

logger = logging.getLogger(__name__)


class FallbackPipeline:
    """Redis 연결 실패 시 사용하는 fallback 파이프라인"""

    def __init__(self):
        self.commands = []

    def set(self, key, value, ex=None):
        """SET 명령어 (실제로는 무시)"""
        self.commands.append(("SET", key, value, ex))
        return self

    def publish(self, channel, message):
        """PUBLISH 명령어 (실제로는 무시)"""
        self.commands.append(("PUBLISH", channel, message))
        return self

    def execute(self):
        """파이프라인 실행 (실제로는 무시)"""
        logger.debug(
            f"[REDIS] Fallback pipeline executed {len(self.commands)} commands (ignored)"
        )
        self.commands.clear()
        return []


class RedisManager:
    def __init__(self, **params):
        self.host = params["host"]
        self.port = params["port"]
        password = params["password"]
        if password == "" or not password:
            self.password = None
        else:
            self.password = password

        self.is_connected_ = False
        self.ssl = params.get("ssl", False)
        self.ssl_ca_certs = params.get("ssl_ca_certs", None)
        self.ssl_cert_reqs = params.get("ssl_cert_reqs", None)
        self.ssl_certfile = params.get("ssl_certfile", None)
        self.ssl_keyfile = params.get("ssl_keyfile", None)

        self.auto_conn = params.get("options", {}).get("auto_conn", True)
        self.auto_utc_set = params.get("options", {}).get("auto_utc_set", True)

        # 연결 실패 대응 메커니즘
        self.max_retry_attempts = 3
        self.retry_delay = 5.0  # 5초
        self.last_connection_attempt = 0
        self.connection_lock = threading.RLock()
        self.fallback_mode = False  # Redis 장애 시 fallback 모드

        # 연결 실패 통계
        self.connection_failures = 0
        self.last_failure_time = 0

        if self.auto_conn:
            self.connect()

    def _created_conn_contents(self):
        return redis.StrictRedis(
            host=self.host,
            port=self.port,
            password=self.password,
            ssl=self.ssl,
            ssl_cert_reqs=self.ssl_cert_reqs,
            ssl_certfile=self.ssl_certfile,
            ssl_keyfile=self.ssl_keyfile,
        )

    def connect(self, force_retry=False):
        """Redis 연결 (재시도 메커니즘 포함)"""
        with self.connection_lock:
            current_time = time.time()

            # 너무 자주 재시도하지 않도록 제한
            if (
                not force_retry
                and (current_time - self.last_connection_attempt) < self.retry_delay
            ):
                return self.is_connected_

            self.last_connection_attempt = current_time

            for attempt in range(self.max_retry_attempts):
                try:
                    self.redis_conn = self._created_conn_contents()
                    logger.info(
                        f"[REDIS] Trying to connect to Redis({self.host}:{self.port}) (attempt {attempt + 1}/{self.max_retry_attempts})"
                    )

                    self.is_connected_ = self.redis_conn.ping()

                    if self.is_connected_:
                        logger.info(f"[REDIS] Connected to Redis successfully")
                        self.fallback_mode = False
                        self.connection_failures = 0
                        return True

                except redis.ConnectionError as e:
                    logger.warning(
                        f"[REDIS] ConnectionError (attempt {attempt + 1}): {e}"
                    )
                    self.connection_failures += 1
                    self.last_failure_time = current_time

                except Exception as e:
                    logger.warning(
                        f"[REDIS] Unexpected error (attempt {attempt + 1}): {e}"
                    )
                    self.connection_failures += 1
                    self.last_failure_time = current_time

                # 마지막 시도가 아니면 잠시 대기
                if attempt < self.max_retry_attempts - 1:
                    time.sleep(1.0)

            # 모든 재시도 실패
            self.is_connected_ = False
            self.fallback_mode = True
            logger.error(
                f"[REDIS] Failed to connect after {self.max_retry_attempts} attempts - entering fallback mode"
            )
            return False

    def get_pipeline(self):
        """파이프라인 획득 (fallback 지원)"""
        if not self.is_connected_:
            if not self.connect():
                # 연결 실패 시 fallback 파이프라인 반환
                return FallbackPipeline()

        try:
            return self.redis_conn.pipeline()
        except Exception as e:
            logger.warning(f"[REDIS] Pipeline creation failed: {e}")
            self.is_connected_ = False
            return FallbackPipeline()

    def SetRedisMsg(self, channel: str, inputs: dict or str, ttl: int = None):
        """Redis 메시지 설정 (fallback 지원)"""
        # fallback 모드에서는 조용히 실패
        if self.fallback_mode:
            logger.debug(f"[REDIS] Fallback mode - skipping SET {channel}")
            return False

        if not self.is_connected_:
            if not self.connect():
                return False

        if isinstance(inputs, str):
            try:
                inputs_dict = json.loads(inputs)
                if not isinstance(inputs_dict, dict):
                    raise ValueError("Invalid JSON format: Not a dictionary")
                inputs = inputs_dict
            except json.JSONDecodeError:
                logger.warning(
                    f"[REDIS] MessageError: Invalid JSON format in input string: {inputs}"
                )
                return False
            except ValueError as ve:
                logger.warning(f"[REDIS] MessageError: {ve}")
                return False

        output = {key: value for key, value in inputs.items() if value is not None}

        if self.auto_utc_set and "utc" not in output:
            output["issued_time"] = (
                datetime.datetime.utcnow().isoformat(timespec="seconds") + "Z"
            )

        if not output:
            logger.warning(f"[REDIS] MessageError: No data to set Redis in: {inputs}")
            return False

        output_json = json.dumps(output)

        try:
            if ttl:
                self.redis_conn.set(channel, output_json, ex=ttl)
                logger.debug(f"[REDIS] SET {channel} (ttl={ttl}): {output_json}")
            else:
                self.redis_conn.set(channel, output_json)
                logger.debug(f"[REDIS] SET {channel}: {output_json}")
            return True
        except Exception as e:
            logger.error(f"[REDIS] Redis SET error: {e}")
            # 연결 상태 재확인
            self.is_connected_ = False
            return False

    def GetRedisMsg(self, channel: str):
        if not self.is_connected_:
            self.connect()

        try:
            if self.redis_conn.exists(channel):
                value_json = self.redis_conn.get(channel)
                value = json.loads(value_json)
                return value
            else:
                logger.warning(f"[REDIS] MessageError: No data from Redis[{channel}]")
        except Exception as e:
            logger.error(f"[REDIS] Redis GET error: {e}")
            return None

    def disconnect(self):
        if not self.is_connected_:
            return False
        try:
            self.redis_conn.close()
            self.is_connected_ = False
            logger.info("[REDIS] Disconnected from Redis")
            return True
        except Exception as e:
            logger.error(f"[REDIS] Error during Redis disconnect: {e}")
            return False

    @property
    def is_connected(self):
        return self.is_connected_

    def get_connection_status(self):
        """연결 상태 및 통계 정보 반환"""
        return {
            "connected": self.is_connected_,
            "fallback_mode": self.fallback_mode,
            "connection_failures": self.connection_failures,
            "last_failure_time": self.last_failure_time,
            "last_connection_attempt": self.last_connection_attempt,
        }

    def force_reconnect(self):
        """강제 재연결 시도"""
        logger.info("[REDIS] Force reconnection requested")
        self.fallback_mode = False
        return self.connect(force_retry=True)

    def publish_message(self, channel: str, message: str):
        """Redis pub/sub 메시지 발행"""
        # fallback 모드에서는 조용히 실패
        if self.fallback_mode:
            logger.debug(f"[REDIS] Fallback mode - skipping PUBLISH {channel}")
            return False

        if not self.is_connected_:
            if not self.connect():
                return False

        try:
            result = self.redis_conn.publish(channel, message)
            logger.debug(
                f"[REDIS] PUBLISH {channel}: {len(message)} bytes, {result} subscribers"
            )
            return True
        except Exception as e:
            logger.error(f"[REDIS] Redis PUBLISH error: {e}")
            # 연결 상태 재확인
            self.is_connected_ = False
            return False
