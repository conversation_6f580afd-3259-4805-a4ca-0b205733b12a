# 파일명: utils/socket_helpers.py

import socket
import struct


def _is_multicast_ip(ip: str):
    """
    주어진 IP가 멀티캐스트(********* ~ ***************) 범위인지 확인
    """
    try:
        first_octet = int(ip.split(".")[0])
        return 224 <= first_octet <= 239
    except Exception:
        return False


def _is_broadcast_ip(ip: str):
    """
    주어진 IP가 브로드캐스트(마지막 옥텟 255)인지 확인
    """
    try:
        last_octet = int(ip.split(".")[-1])
        return last_octet == 255
    except Exception:
        return False


def _configure_multicast_socket(
    raw_socket: socket.socket, multicast_group: str, port: int, interface_ip: str | None
):
    """
    멀티캐스트 그룹 가입 설정
    - raw_socket: AF_INET/UDP 소켓
    - multicast_group: "239.x.x.x" 형태의 그룹 주소
    - port: 수신 포트
    - interface_ip: 로컬 NIC IP (None일 경우 INADDR_ANY)
    """
    try:
        if interface_ip is None:
            mreq = struct.pack(
                "4sl", socket.inet_aton(multicast_group), socket.INADDR_ANY
            )
        else:
            mreq = struct.pack(
                "4s4s",
                socket.inet_aton(multicast_group),
                socket.inet_aton(interface_ip),
            )
        raw_socket.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)
    except Exception as e:
        raise RuntimeError(
            f"멤버십 가입 실패: {multicast_group}:{port} on {interface_ip}: {e}"
        )


def _configure_broadcast_socket(raw_socket: socket.socket):
    """
    브로드캐스트 송수신을 위해 SO_BROADCAST 옵션 설정
    """
    raw_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
