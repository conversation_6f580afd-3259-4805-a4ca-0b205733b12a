import os
import logging
from multiprocessing import cpu_count


ADTA_LOG_LEVELS = os.getenv("ADTA_LOG_LEVELS", "").lower()
# ADTA_LOG_LEVELS = os.getenv("ADTA_LOG_LEVELS", "debug").lower()

ADTA_HINAS_IP = os.getenv("ADTA_HINAS_IP", "***********")

REDIS_KEY_PREFIX = os.getenv("REDIS_KEY_PREFIX", "").upper()


CONFIG_SENSOR_PATH = os.getenv("CONFIG_SENSOR_PATH", "config/config_sensor.yaml")
CONFIG_DEVICE_PATH = os.getenv("CONFIG_DEVICE_PATH", "config/config_source_device.yaml")
DEFINE_SENTENCE_MAP_PATH = os.getenv(
    "DEFINE_SENTENCE_MAP_PATH", "config/define_data_sentence.yaml"
)

# Define log level choices
LOG_LEVELS = {
    "debug": logging.DEBUG,
    "info": logging.INFO,
    "warning": logging.WARNING,
    "error": logging.ERROR,
    "critical": logging.CRITICAL,
}

REDIS_PARAMS = {
    "host": os.getenv("REDIS_HOST", "***************"),
    "port": int(os.getenv("REDIS_PORT", "6379")),
    "password": os.getenv("REDIS_PASSWORD", ""),
    "ssl": os.getenv("REDIS_SSL", "False").lower() in ("true", "1", "yes"),
    "ssl_ca_certs": os.getenv("REDIS_SSL_CA_CERTS", None),
    "ssl_certfile": os.getenv("REDIS_SSL_CERTFILE", None),
    "ssl_keyfile": os.getenv("REDIS_SSL_KEYFILE", None),
    "options": {
        "auto_conn": os.getenv("REDIS_AUTO_CONN", "True").lower()
        in ("true", "1", "yes"),
        "auto_utc_set": os.getenv("REDIS_AUTO_UTC_SET", "True").lower()
        in ("true", "1", "yes"),
    },
}

PARSE_WORKERS = int(os.getenv("PARSE_WORKERS", max(1, cpu_count() // 2)))
SAVE_WORKERS = int(os.getenv("SAVE_WORKERS", max(1, cpu_count() // 2)))
QUEUE_SIZE = int(os.getenv("QUEUE_SIZE", 20000))

## ENV 기타 환경 변수
os.environ["PYDANTIC_ERRORS_INCLUDE_URL"] = "0"

## ERROR 관련 환경변수
ERROR_COUNTER_MAX = int(os.getenv("ERROR_COUNTER_MAX", "999999999"))
ERROR_LOG_MAX = int(os.getenv("ERROR_LOG_MAX", "100"))
ERROR_LOG_FLUSH_INTERVAL = float(os.getenv("ERROR_LOG_FLUSH_INTERVAL", "0.5"))
ERROR_COUNTER_FLUSH_THRESHOLD = int(os.getenv("ERROR_COUNTER_FLUSH_THRESHOLD", "200"))


## API 관련 환경변수
API_HOST = "0.0.0.0"
API_PORT = int("9999")
SHUTDOWN_TOKEN = os.getenv("ADTA_SHUTDOWN_TOKEN", "avikus123").strip()


## AUTH 관련 환경변수
# SSO SETTINGS
class AuthConfig:
    PRODUCTION_BASE_PATH = os.getenv("PRODUCTION_BASE_PATH", "")
    AUTH_HOST = os.getenv("AUTH_HOST", "https://sso.ingress.local")
    CONTROL_HOST = os.getenv("CONTROL_HOST", "http://localhost:9999/")
    CONTROL_LOGIN = os.getenv(
        "CONTROL_LOGIN", "http://localhost:9999/api/oauth2/session"
    )
    SESSION_CHECK_INTERVAL: int = int(os.getenv("SESSION_CHECK_INTERVAL", "5"))

    SSO_LOGIN_URL = os.getenv("SSO_LOGIN_URL", "https://sso.ingress.local/home")
    SSO_LOGOUT_URL = os.getenv(
        "SSO_LOGOUT_URL", "https://sso.ingress.local/sso-api/v1/oauth2/logout"
    )
    CLIENT_ID = os.getenv("CLIENT_ID", "adta_app")
    CLIENT_SECRET = os.getenv("CLIENT_SECRET", "adta_secret")


# 외부 Auth 모듈 API 엔드포인트
class AuthModuleAPI:
    CREATE_SESSION = f"{AuthConfig.AUTH_HOST}/sso-api/v1/oauth2/session"
    REFRESH_SESSION = f"{AuthConfig.AUTH_HOST}/sso-api/v1/oauth2/session/refresh"
    OWN_INFO = f"{AuthConfig.AUTH_HOST}/sso-api/v1/oauth2/me"
    HEALTH_CHECK = f"{AuthConfig.AUTH_HOST}/sso-api/v1/oauth2/health"


# Only Development Option
AUTH_MODULE_ENABLED = os.getenv("AUTH_MODULE_ENABLED", "true").lower() in [
    "true",
    "1",
    "yes",
]
