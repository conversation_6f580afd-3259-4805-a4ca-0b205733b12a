from enum import Enum
import types


def full_model_dump(obj, visited=None, depth=0, max_depth=100):
    # if isssubclass(obj, NmeaSentenceBase): #obj가 class인 경우 사용함

    if visited is None:
        visited = set()

    if depth > max_depth:
        raise RuntimeError(
            f"Max recursion depth ({max_depth}) exceeded in full_model_dump"
        )

    # 기본 타입은 바로 리턴
    if isinstance(obj, (int, float, str, bool, type(None))):
        return obj

    # Enum은 name으로
    if isinstance(obj, Enum):
        return obj.name

    # bytes -> hex
    if isinstance(obj, bytes):
        return obj.hex()

    # 함수/메서드 무시
    if isinstance(
        obj, (types.BuiltinFunctionType, types.FunctionType, types.MethodType)
    ):
        return f"<non-serializable: {type(obj).__name__}>"

    # 순환 체크
    obj_id = id(obj)
    if obj_id in visited:
        raise RuntimeError(f"Circular reference detected for object id={obj_id}")
    visited.add(obj_id)

    # 리스트 처리
    if isinstance(obj, list):
        return [full_model_dump(i, visited, depth + 1, max_depth) for i in obj]

    # 딕셔너리 처리
    if isinstance(obj, dict):
        return {
            str(k): full_model_dump(v, visited, depth + 1, max_depth)
            for k, v in obj.items()
        }

    # 객체 처리 (__dict__)
    if hasattr(obj, "__dict__"):
        result = {}
        for k, v in obj.__dict__.items():
            result[k] = full_model_dump(v, visited, depth + 1, max_depth)
        return result

    # 나머지 기본 타입은 string 처리
    return str(obj)
