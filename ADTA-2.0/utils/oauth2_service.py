# app/service/oauth2_service.py
import time
import requests
import urllib3
from utils.config import AuthConfig, AuthModuleAPI

# SSL 경고 메시지 비활성화
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 예외 정의


class UnauthenticatedException(Exception):
    def __init__(self, detail="", message="unauthenticated"):
        self.message = message
        self.detail = detail


class NotFoundException(Exception):
    def __init__(self, detail="", message="not found"):
        self.message = message
        self.detail = detail


class WrongSessionException(UnauthenticatedException):
    def __init__(self, message="wrong session id", detail=None):
        super().__init__(detail)
        self.message = message
        self.detail = detail


class AuthModuleNotFoundException(NotFoundException):
    def __init__(self, message="auth module is not working.", detail=None):
        super().__init__(detail)
        self.message = message
        self.detail = detail


class Oauth2Service:
    @classmethod
    def get_client(cls):
        result = {
            "login_url": AuthConfig.SSO_LOGIN_URL,
            "logout_url": AuthConfig.SSO_LOGOUT_URL,
            "client_id": AuthConfig.CLIENT_ID,
        }
        return result

    @classmethod
    def create_session(cls, code: str, device_id: str):
        body = {
            "client_id": AuthConfig.CLIENT_ID,
            "client_secret": AuthConfig.CLIENT_SECRET,
            "code": code,
        }
        url = AuthModuleAPI.CREATE_SESSION
        # (실제 호출 전 약간의 딜레이를 시뮬레이션)
        time.sleep(0.7)
        response = requests.post(url, json=body, verify=False, timeout=5).json()
        if response.get("code") != 200000:
            raise WrongSessionException(response.get("message"))
        result = response.get("result")
        session_id = result.get("session_id")
        expire_time = result.get("expire_time")
        return result, session_id, expire_time, device_id

    @classmethod
    def refresh_session(cls, session_id: str):
        headers = {"Cookie": f"session_id={session_id}"}
        url = AuthModuleAPI.REFRESH_SESSION
        response = requests.post(url, headers=headers, verify=False, timeout=5).json()
        if response.get("code") != 200000:
            raise WrongSessionException(response.get("message"))
        expire_time = response.get("result").get("expire_time")
        return response, session_id, expire_time

    @classmethod
    def get_me(cls, session_id: str):
        headers = {"Cookie": f"session_id={session_id}"}
        url = AuthModuleAPI.OWN_INFO
        response = requests.get(url, headers=headers, verify=False, timeout=5).json()
        if response.get("code") != 200000:
            raise WrongSessionException(response.get("message"))
        return response

    @classmethod
    def get_health(cls):
        url = AuthModuleAPI.HEALTH_CHECK
        response = requests.get(url, verify=False, timeout=5).json()
        if response.get("code") != 200000:
            raise AuthModuleNotFoundException(response.get("message"))
        return response
