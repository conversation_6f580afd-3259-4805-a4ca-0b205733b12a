<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Raw Packet Stream Monitor</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1e1e1e;
            color: #d4d4d4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background-color: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        input, button, select {
            padding: 8px 12px;
            border: 1px solid #3e3e42;
            background-color: #2d2d30;
            color: #d4d4d4;
            border-radius: 4px;
        }
        button {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #3e3e42;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .start-btn {
            background-color: #0e639c;
        }
        .stop-btn {
            background-color: #a1260d;
        }
        .status {
            background-color: #2d2d30;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-item {
            display: inline-block;
            margin-right: 20px;
            padding: 5px 10px;
            background-color: #3e3e42;
            border-radius: 4px;
        }
        .packet-display {
            background-color: #0d1117;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            height: 500px;
            overflow-y: auto;
            padding: 15px;
            font-size: 12px;
            line-height: 1.4;
        }
        .packet {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #161b22;
            border-left: 3px solid #0969da;
            border-radius: 4px;
        }
        .packet-header {
            color: #7c3aed;
            font-weight: bold;
        }
        .packet-data {
            color: #22c55e;
            margin-top: 4px;
        }
        .system-message {
            color: #f59e0b;
            font-style: italic;
            border-left-color: #f59e0b;
        }
        .error-message {
            color: #ef4444;
            border-left-color: #ef4444;
        }
        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 11px;
            color: #8b949e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Raw Packet Stream Monitor</h1>
            <div class="controls">
                <div class="control-group">
                    <label>서버:</label>
                    <input type="text" id="serverUrl" value="http://localhost:9999" placeholder="서버 URL">
                </div>
                <div class="control-group">
                    <label>지속시간:</label>
                    <select id="duration">
                        <option value="10">10초</option>
                        <option value="30" selected>30초</option>
                        <option value="60">1분</option>
                        <option value="120">2분</option>
                        <option value="300">5분</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>인증토큰:</label>
                    <input type="password" id="authToken" placeholder="선택사항">
                </div>
                <button id="startBtn" class="start-btn">🚀 시작</button>
                <button id="stopBtn" class="stop-btn" disabled>🛑 중지</button>
                <button id="clearBtn">🗑️ 클리어</button>
            </div>
        </div>

        <div class="status">
            <span class="status-item">상태: <span id="connectionStatus">연결 안됨</span></span>
            <span class="status-item">패킷: <span id="packetCount">0</span></span>
            <span class="status-item">속도: <span id="packetRate">0</span>/초</span>
            <span class="status-item">지속시간: <span id="elapsedTime">0</span>초</span>
        </div>

        <div class="packet-display" id="packetDisplay">
            <div class="packet system-message">
                <div class="packet-header">[SYSTEM]</div>
                <div class="packet-data">Raw 패킷 모니터링 준비 완료. 시작 버튼을 클릭하세요.</div>
            </div>
        </div>

        <div class="stats">
            <span>tcpdump 스타일 실시간 패킷 모니터링</span>
            <span>최대 연결: 3개 | 자동 종료: 설정된 시간 후</span>
        </div>
    </div>

    <script>
        class RawPacketMonitor {
            constructor() {
                this.eventSource = null;
                this.isConnected = false;
                this.packetCount = 0;
                this.startTime = null;
                this.rateInterval = null;
                
                this.initElements();
                this.bindEvents();
            }
            
            initElements() {
                this.serverUrl = document.getElementById('serverUrl');
                this.duration = document.getElementById('duration');
                this.authToken = document.getElementById('authToken');
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.connectionStatus = document.getElementById('connectionStatus');
                this.packetCountEl = document.getElementById('packetCount');
                this.packetRate = document.getElementById('packetRate');
                this.elapsedTime = document.getElementById('elapsedTime');
                this.packetDisplay = document.getElementById('packetDisplay');
            }
            
            bindEvents() {
                this.startBtn.addEventListener('click', () => this.startMonitoring());
                this.stopBtn.addEventListener('click', () => this.stopMonitoring());
                this.clearBtn.addEventListener('click', () => this.clearDisplay());
            }
            
            startMonitoring() {
                if (this.isConnected) return;
                
                const serverUrl = this.serverUrl.value.trim();
                const duration = this.duration.value;
                const authToken = this.authToken.value.trim();
                
                if (!serverUrl) {
                    this.addSystemMessage('서버 URL을 입력하세요.');
                    return;
                }
                
                // URL 구성
                let url = `${serverUrl}/api/raw-monitor/stream?max_duration=${duration}`;
                if (authToken) {
                    url += `&auth_token=${authToken}`;
                }
                
                this.addSystemMessage(`연결 중: ${url}`);
                
                // EventSource 생성
                this.eventSource = new EventSource(url);
                this.startTime = Date.now();
                this.packetCount = 0;
                
                this.eventSource.onopen = () => {
                    this.isConnected = true;
                    this.updateConnectionStatus('연결됨', '#22c55e');
                    this.startBtn.disabled = true;
                    this.stopBtn.disabled = false;
                    this.addSystemMessage('✅ SSE 연결 성공! 패킷 수신 중...');
                    this.addSystemMessage(`🔗 연결 URL: ${url}`);
                    this.startRateCalculation();
                    console.log('[DEBUG] SSE connection opened successfully');
                };
                
                this.eventSource.onmessage = (event) => {
                    console.log('[DEBUG] SSE message received:', event.data);
                    this.handlePacketData(event.data);
                };

                this.eventSource.onerror = (error) => {
                    console.error('[DEBUG] SSE error:', error);
                    console.log('[DEBUG] EventSource readyState:', this.eventSource.readyState);

                    // readyState 확인
                    if (this.eventSource.readyState === EventSource.CONNECTING) {
                        this.addSystemMessage('🔄 연결 재시도 중...');
                    } else if (this.eventSource.readyState === EventSource.CLOSED) {
                        this.addErrorMessage('❌ 연결이 닫혔습니다');
                        this.stopMonitoring();
                    } else {
                        this.addErrorMessage('❌ 연결 오류 발생');
                        this.stopMonitoring();
                    }
                };
            }
            
            stopMonitoring() {
                if (this.eventSource) {
                    this.eventSource.close();
                    this.eventSource = null;
                }
                
                this.isConnected = false;
                this.updateConnectionStatus('연결 안됨', '#ef4444');
                this.startBtn.disabled = false;
                this.stopBtn.disabled = true;
                this.stopRateCalculation();
                this.addSystemMessage('🛑 모니터링 중지됨');
            }
            
            handlePacketData(data) {
                console.log('[DEBUG] Processing packet data:', data.substring(0, 100) + '...');

                if (data.startsWith('[STREAM_START]') || data.startsWith('[STREAM_END]') ||
                    data.startsWith('[HEARTBEAT]')) {
                    this.addSystemMessage(data);
                    console.log('[DEBUG] System message:', data);

                    if (data.startsWith('[STREAM_END]')) {
                        this.stopMonitoring();
                    }
                } else if (data.startsWith('[ERROR]')) {
                    this.addErrorMessage(data);
                    console.error('[DEBUG] Error message:', data);
                } else if (data.includes(']:')) {
                    // 실제 패킷 데이터
                    console.log('[DEBUG] Packet data received:', data.substring(0, 50) + '...');
                    this.addPacketData(data);
                    this.packetCount++;
                    this.packetCountEl.textContent = this.packetCount;
                } else {
                    // 알 수 없는 데이터
                    console.log('[DEBUG] Unknown data format:', data);
                    this.addSystemMessage(`🔍 Unknown data: ${data.substring(0, 100)}...`);
                }
            }
            
            addPacketData(data) {
                const lines = data.split('\\n');
                const header = lines[0] || data;
                const content = lines.slice(1).join('\\n') || '';
                
                const packetEl = document.createElement('div');
                packetEl.className = 'packet';
                packetEl.innerHTML = `
                    <div class="packet-header">${this.escapeHtml(header)}</div>
                    ${content ? `<div class="packet-data">${this.escapeHtml(content)}</div>` : ''}
                `;
                
                this.packetDisplay.appendChild(packetEl);
                this.scrollToBottom();
                
                // 패킷이 너무 많으면 오래된 것 제거
                if (this.packetDisplay.children.length > 500) {
                    this.packetDisplay.removeChild(this.packetDisplay.firstChild);
                }
            }
            
            addSystemMessage(message) {
                const packetEl = document.createElement('div');
                packetEl.className = 'packet system-message';
                packetEl.innerHTML = `
                    <div class="packet-header">[SYSTEM]</div>
                    <div class="packet-data">${this.escapeHtml(message)}</div>
                `;
                this.packetDisplay.appendChild(packetEl);
                this.scrollToBottom();
            }
            
            addErrorMessage(message) {
                const packetEl = document.createElement('div');
                packetEl.className = 'packet error-message';
                packetEl.innerHTML = `
                    <div class="packet-header">[ERROR]</div>
                    <div class="packet-data">${this.escapeHtml(message)}</div>
                `;
                this.packetDisplay.appendChild(packetEl);
                this.scrollToBottom();
            }
            
            clearDisplay() {
                this.packetDisplay.innerHTML = `
                    <div class="packet system-message">
                        <div class="packet-header">[SYSTEM]</div>
                        <div class="packet-data">디스플레이가 클리어되었습니다.</div>
                    </div>
                `;
                this.packetCount = 0;
                this.packetCountEl.textContent = '0';
            }
            
            updateConnectionStatus(status, color) {
                this.connectionStatus.textContent = status;
                this.connectionStatus.style.color = color;
            }
            
            startRateCalculation() {
                this.rateInterval = setInterval(() => {
                    if (this.startTime) {
                        const elapsed = (Date.now() - this.startTime) / 1000;
                        const rate = elapsed > 0 ? (this.packetCount / elapsed).toFixed(1) : '0';
                        this.packetRate.textContent = rate;
                        this.elapsedTime.textContent = Math.floor(elapsed);
                    }
                }, 1000);
            }
            
            stopRateCalculation() {
                if (this.rateInterval) {
                    clearInterval(this.rateInterval);
                    this.rateInterval = null;
                }
            }
            
            scrollToBottom() {
                this.packetDisplay.scrollTop = this.packetDisplay.scrollHeight;
            }
            
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }
        
        // 초기화
        document.addEventListener('DOMContentLoaded', () => {
            new RawPacketMonitor();
        });
    </script>
</body>
</html>
