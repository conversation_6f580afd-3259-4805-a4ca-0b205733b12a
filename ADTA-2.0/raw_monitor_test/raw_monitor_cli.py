#!/usr/bin/env python3
"""
Raw Stream Monitor CLI

터미널에서 실시간 패킷 모니터링을 위한 CLI 도구
"""

import requests
import time
import argparse
import sys
import signal
from datetime import datetime
from typing import Optional

class RawStreamMonitorCLI:
    def __init__(self, base_url: str = "http://localhost:9999"):
        self.base_url = base_url
        self.running = False
        self.packet_count = 0
        self.start_time = None
        
        # Ctrl+C 핸들러 등록
        signal.signal(signal.SIGINT, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Ctrl+C 핸들러"""
        print("\n🛑 중지 신호 받음. 정리 중...")
        self.running = False
    
    def print_status(self):
        """현재 상태 출력"""
        try:
            response = requests.get(f"{self.base_url}/api/raw-monitor/stream/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print("📊 현재 상태:")
                print(f"   활성 연결: {data['active_connections']}/{data['max_connections']}")
                print(f"   버퍼 크기: {data['buffer_size']}")
                print(f"   최대 지속시간: {data['max_duration']}초")
                print(f"   인증 활성화: {data['auth_enabled']}")
                return True
            else:
                print(f"❌ 상태 조회 실패: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 상태 조회 오류: {e}")
            return False
    
    def format_packet_display(self, packet_data: str) -> str:
        """패킷 데이터를 보기 좋게 포맷팅"""
        if "[STREAM_START]" in packet_data:
            return f"🟢 {packet_data}"
        elif "[STREAM_END]" in packet_data:
            return f"🔴 {packet_data}"
        elif "[HEARTBEAT]" in packet_data:
            return f"💓 {packet_data}"
        elif "[ERROR]" in packet_data:
            return f"❌ {packet_data}"
        elif packet_data.startswith("[") and "]" in packet_data:
            # 실제 패킷 데이터
            lines = packet_data.split("\\n")
            if len(lines) > 1:
                header = lines[0]
                content = "\\n".join(lines[1:])
                return f"📦 {header}\n    {content}"
            else:
                return f"📦 {packet_data}"
        else:
            return packet_data
    
    def monitor_stream(self, duration: int = 60, auth_token: Optional[str] = None, 
                      show_heartbeat: bool = False, max_packets: int = 0):
        """실시간 스트림 모니터링"""
        # URL 구성
        url = f"{self.base_url}/api/raw-monitor/stream?max_duration={duration}"
        if auth_token:
            url += f"&auth_token={auth_token}"
        
        print(f"🚀 Raw 패킷 스트림 모니터링 시작")
        print(f"   URL: {url}")
        print(f"   지속시간: {duration}초")
        print(f"   최대 패킷: {max_packets if max_packets > 0 else '무제한'}")
        print("   Ctrl+C로 중지")
        print("=" * 60)
        
        self.running = True
        self.packet_count = 0
        self.start_time = time.time()
        
        try:
            with requests.get(url, stream=True, timeout=duration + 10) as response:
                if response.status_code != 200:
                    print(f"❌ 연결 실패: {response.status_code}")
                    if response.status_code == 401:
                        print("   인증 토큰이 필요하거나 잘못되었습니다.")
                    elif response.status_code == 429:
                        print("   최대 연결 수를 초과했습니다.")
                    return False
                
                print("✅ SSE 연결 성공! 패킷 수신 중...\n")
                
                for line in response.iter_lines(decode_unicode=True):
                    if not self.running:
                        break
                    
                    if line.startswith('data: '):
                        packet_data = line[6:]  # 'data: ' 제거
                        
                        # 패킷 타입별 처리
                        if "[HEARTBEAT]" in packet_data and not show_heartbeat:
                            continue  # heartbeat 숨기기
                        
                        # 패킷 출력
                        formatted = self.format_packet_display(packet_data)
                        print(formatted)
                        
                        # 실제 패킷인 경우 카운트
                        if packet_data.startswith("[") and "]" in packet_data and \
                           not any(x in packet_data for x in ["STREAM", "HEARTBEAT", "ERROR"]):
                            self.packet_count += 1
                            
                            # 최대 패킷 수 체크
                            if max_packets > 0 and self.packet_count >= max_packets:
                                print(f"\n🎯 최대 패킷 수({max_packets})에 도달했습니다.")
                                break
                        
                        # 스트림 종료 메시지 확인
                        if "[STREAM_END]" in packet_data:
                            break
                
                # 통계 출력
                elapsed = time.time() - self.start_time
                rate = self.packet_count / elapsed if elapsed > 0 else 0
                
                print("\n" + "=" * 60)
                print("📊 모니터링 완료:")
                print(f"   지속시간: {elapsed:.1f}초")
                print(f"   수신 패킷: {self.packet_count}개")
                print(f"   평균 속도: {rate:.1f} 패킷/초")
                
                return True
                
        except KeyboardInterrupt:
            print("\n🛑 사용자에 의해 중지됨")
            return True
        except Exception as e:
            print(f"\n❌ 스트림 오류: {e}")
            return False
    
    def test_connection_limit(self):
        """연결 제한 테스트"""
        print("🔒 연결 제한 테스트")
        print("=" * 40)
        
        import threading
        import queue
        
        results = queue.Queue()
        
        def test_connection(conn_id):
            try:
                url = f"{self.base_url}/api/raw-monitor/stream?max_duration=10"
                response = requests.get(url, stream=True, timeout=5)
                results.put((conn_id, response.status_code))
                if response.status_code == 200:
                    # 연결 유지
                    time.sleep(2)
                response.close()
            except Exception as e:
                results.put((conn_id, f"Error: {e}"))
        
        # 4개 연결 동시 시도
        threads = []
        for i in range(4):
            thread = threading.Thread(target=test_connection, args=(i+1,))
            threads.append(thread)
            thread.start()
            time.sleep(0.2)  # 약간의 간격
        
        # 결과 수집
        for thread in threads:
            thread.join()
        
        # 결과 출력
        while not results.empty():
            conn_id, status = results.get()
            if status == 200:
                print(f"   연결 {conn_id}: ✅ 성공 (200)")
            elif status == 429:
                print(f"   연결 {conn_id}: 🚫 제한됨 (429)")
            else:
                print(f"   연결 {conn_id}: ❌ 실패 ({status})")
        
        print("✅ 연결 제한 테스트 완료")

def main():
    parser = argparse.ArgumentParser(description="Raw Stream Monitor CLI")
    parser.add_argument("--url", default="http://localhost:9999", 
                       help="서버 URL (기본: http://localhost:9999)")
    parser.add_argument("-d", "--duration", type=int, default=60,
                       help="모니터링 지속시간 (초, 기본: 60)")
    parser.add_argument("-t", "--token", help="인증 토큰")
    parser.add_argument("--heartbeat", action="store_true", 
                       help="heartbeat 메시지 표시")
    parser.add_argument("--max-packets", type=int, default=0,
                       help="최대 수신 패킷 수 (0=무제한)")
    parser.add_argument("--status", action="store_true",
                       help="상태만 확인하고 종료")
    parser.add_argument("--test-limit", action="store_true",
                       help="연결 제한 테스트")
    
    args = parser.parse_args()
    
    monitor = RawStreamMonitorCLI(args.url)
    
    if args.status:
        monitor.print_status()
        return
    
    if args.test_limit:
        monitor.test_connection_limit()
        return
    
    # 기본 모니터링 실행
    print("🔍 Raw Stream Monitor CLI")
    print("=" * 40)
    
    # 먼저 상태 확인
    if not monitor.print_status():
        print("서버 연결을 확인해주세요.")
        sys.exit(1)
    
    print()
    
    # 스트림 모니터링 시작
    success = monitor.monitor_stream(
        duration=args.duration,
        auth_token=args.token,
        show_heartbeat=args.heartbeat,
        max_packets=args.max_packets
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
