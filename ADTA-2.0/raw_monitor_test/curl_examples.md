# Raw Stream Monitor - curl 테스트 예제

## 🚀 빠른 테스트 명령어들

### 1. 상태 확인
```bash
curl -s http://localhost:9999/api/raw-monitor/stream/status | python3 -m json.tool
```

### 2. 30초 스트림 모니터링
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=30"
```

### 3. 인증 토큰과 함께 스트림
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=60&auth_token=your_token"
```

### 4. 패킷 카운트와 함께 모니터링
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=30" | \
awk '/^data: \[.*\]:/ && !/STREAM|HEARTBEAT|ERROR/ {count++; print "["count"]", $0} 
     /^data: \[STREAM|HEARTBEAT|ERROR/ {print "🔔", $0}'
```

### 5. 실시간 패킷 카운터
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=60" | \
grep "^data: \[" | grep -v "STREAM\|HEARTBEAT\|ERROR" | \
nl -nln -w3 -s": "
```

### 6. 타임스탬프와 함께 로깅
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=30" | \
while IFS= read -r line; do
    if [[ $line == data:* ]]; then
        echo "[$(date '+%H:%M:%S')] ${line#data: }"
    fi
done
```

### 7. 패킷을 파일로 저장
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=60" | \
grep "^data: \[" | sed 's/^data: //' > raw_packets_$(date +%Y%m%d_%H%M%S).log
```

### 8. 연결 제한 테스트 (백그라운드 4개 연결)
```bash
for i in {1..4}; do
    echo "Connection $i..."
    timeout 5 curl -s "http://localhost:9999/api/raw-monitor/stream?max_duration=10" > /dev/null &
    sleep 0.5
done
wait
```

### 9. 특정 패킷만 필터링 (예: MISC 서버)
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=30" | \
grep "^data: \[.*MISC:" | sed 's/^data: //'
```

### 10. 실시간 통계 (1초마다 패킷 수 출력)
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=60" | \
grep "^data: \[" | grep -v "STREAM\|HEARTBEAT\|ERROR" | \
(count=0; while read line; do 
    count=$((count+1))
    echo -ne "\rPackets: $count"
done; echo)
```

## 🔧 고급 사용법

### jq를 사용한 상태 정보 파싱
```bash
curl -s http://localhost:9999/api/raw-monitor/stream/status | \
jq -r '"Active: \(.active_connections)/\(.max_connections), Buffer: \(.buffer_size), Auth: \(.auth_enabled)"'
```

### 패킷 속도 측정
```bash
start_time=$(date +%s)
packet_count=$(curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=10" | \
               grep -c "^data: \[.*\]:" | grep -v "STREAM\|HEARTBEAT\|ERROR")
end_time=$(date +%s)
duration=$((end_time - start_time))
echo "Rate: $((packet_count / duration)) packets/sec"
```

### 에러 모니터링
```bash
curl -s -N "http://localhost:9999/api/raw-monitor/stream?max_duration=30" | \
grep "ERROR\|STREAM_END" | sed 's/^data: /[ERROR] /'
```

## 🐛 디버깅 명령어

### 연결 상태 확인
```bash
curl -I http://localhost:9999/api/raw-monitor/stream/status
```

### 서버 응답 시간 측정
```bash
curl -w "@-" -o /dev/null -s "http://localhost:9999/api/raw-monitor/stream/status" <<'EOF'
     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n
EOF
```

### 헤더 정보 확인
```bash
curl -v "http://localhost:9999/api/raw-monitor/stream?max_duration=5" 2>&1 | head -20
```
