#!/usr/bin/env python3
"""
실시간 RAW 패킷 SSE 스트리밍 테스트

Server-Sent Events를 통한 실시간 tcpdump 스타일 패킷 모니터링 테스트
"""

import requests
import time
import threading
from typing import Optional

API_BASE = "http://localhost:9999"

def test_sse_stream(duration: int = 10, auth_token: Optional[str] = None):
    """SSE 스트림 테스트"""
    print(f"🧪 SSE Stream Test ({duration}초)")
    print("=" * 50)
    
    # URL 구성
    url = f"{API_BASE}/api/raw-monitor/stream?max_duration={duration}"
    if auth_token:
        url += f"&auth_token={auth_token}"
    
    print(f"📡 Connecting to: {url}")
    
    try:
        # SSE 스트림 연결
        with requests.get(url, stream=True, timeout=duration + 5) as response:
            if response.status_code != 200:
                print(f"❌ Connection failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
            
            print(f"✅ Connected! Status: {response.status_code}")
            print("📦 Receiving packets...")
            print("-" * 60)
            
            packet_count = 0
            start_time = time.time()
            
            # SSE 데이터 읽기
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data: '):
                    packet_data = line[6:]  # 'data: ' 제거
                    
                    # 패킷 출력
                    if packet_data.startswith('[') and not packet_data.startswith('[STREAM'):
                        packet_count += 1
                        print(f"#{packet_count}: {packet_data}")
                    elif packet_data.startswith('[STREAM') or packet_data.startswith('[HEARTBEAT'):
                        print(f"🔔 {packet_data}")
                    
                    # 스트림 종료 메시지 확인
                    if '[STREAM_END]' in packet_data:
                        print(f"🏁 Stream ended: {packet_data}")
                        break
                
                # 타임아웃 체크
                if time.time() - start_time > duration + 2:
                    print("⏰ Timeout reached")
                    break
            
            elapsed = time.time() - start_time
            print("-" * 60)
            print(f"📊 Results:")
            print(f"   Duration: {elapsed:.1f}s")
            print(f"   Packets received: {packet_count}")
            print(f"   Rate: {packet_count/elapsed:.1f} packets/sec")
            
            return True
            
    except Exception as e:
        print(f"❌ Stream error: {e}")
        return False

def test_connection_limit():
    """연결 제한 테스트"""
    print("\n🔒 Connection Limit Test")
    print("=" * 50)
    
    connections = []
    
    try:
        # 4개 연결 시도 (최대 3개 제한)
        for i in range(4):
            print(f"📡 Connection {i+1} attempt...")
            
            url = f"{API_BASE}/api/raw-monitor/stream?max_duration=30"
            response = requests.get(url, stream=True, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ Connection {i+1} successful")
                connections.append(response)
            elif response.status_code == 429:
                print(f"🚫 Connection {i+1} rejected (limit reached)")
                print(f"   Response: {response.text}")
                break
            else:
                print(f"❌ Connection {i+1} failed: {response.status_code}")
                break
            
            time.sleep(0.5)
        
        print(f"📊 Active connections: {len(connections)}")
        
        # 연결 정리
        for conn in connections:
            conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Connection limit test error: {e}")
        return False

def test_stream_status():
    """스트림 상태 테스트"""
    print("\n📊 Stream Status Test")
    print("=" * 50)
    
    try:
        response = requests.get(f"{API_BASE}/api/raw-monitor/stream/status")
        
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Status retrieved:")
            print(f"   Active connections: {status['active_connections']}/{status['max_connections']}")
            print(f"   Buffer size: {status['buffer_size']}")
            print(f"   Max duration: {status['max_duration']}s")
            print(f"   Auth enabled: {status['auth_enabled']}")
            return True
        else:
            print(f"❌ Status request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Status test error: {e}")
        return False

def test_auth_protection():
    """인증 보호 테스트"""
    print("\n🔐 Auth Protection Test")
    print("=" * 50)
    
    # 환경변수 확인
    import os
    auth_enabled = os.getenv("RAW_MONITOR_AUTH_ENABLED", "false").lower() == "true"
    
    if not auth_enabled:
        print("⚠️  Auth is disabled (RAW_MONITOR_AUTH_ENABLED=false)")
        print("   Skipping auth test")
        return True
    
    try:
        # 1. 토큰 없이 접근
        print("1️⃣ Testing without token...")
        response = requests.get(f"{API_BASE}/api/raw-monitor/stream?max_duration=5")
        
        if response.status_code == 401:
            print("✅ Correctly rejected without token")
        else:
            print(f"❌ Should be rejected, got: {response.status_code}")
            return False
        
        # 2. 잘못된 토큰으로 접근
        print("2️⃣ Testing with wrong token...")
        response = requests.get(f"{API_BASE}/api/raw-monitor/stream?max_duration=5&auth_token=wrong_token")
        
        if response.status_code == 401:
            print("✅ Correctly rejected with wrong token")
        else:
            print(f"❌ Should be rejected, got: {response.status_code}")
            return False
        
        # 3. 올바른 토큰으로 접근
        print("3️⃣ Testing with correct token...")
        correct_token = os.getenv("RAW_MONITOR_AUTH_TOKEN", "default_secret_token")
        response = requests.get(f"{API_BASE}/api/raw-monitor/stream?max_duration=5&auth_token={correct_token}")
        
        if response.status_code == 200:
            print("✅ Correctly accepted with valid token")
            response.close()
        else:
            print(f"❌ Should be accepted, got: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Auth test error: {e}")
        return False

def main():
    """메인 테스트 실행"""
    print("🚀 Raw Stream Monitor Complete Test Suite")
    print("=" * 70)
    
    tests = [
        ("Stream Status", test_stream_status),
        ("SSE Stream (10s)", lambda: test_sse_stream(10)),
        ("Connection Limit", test_connection_limit),
        ("Auth Protection", test_auth_protection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}: {'통과' if result else '실패'}")
        except Exception as e:
            print(f"❌ {test_name} 실행 중 오류: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # 테스트 간 간격
    
    # 결과 요약
    print("\n" + "=" * 70)
    print("📊 테스트 결과 요약")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 전체 결과: {passed}/{total} 테스트 통과 ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 모든 Raw Stream Monitor 테스트가 성공적으로 완료되었습니다!")
        print("✅ 실시간 SSE 스트리밍이 정상 작동합니다!")
    else:
        print("⚠️  일부 테스트가 실패했습니다.")

if __name__ == "__main__":
    main()
