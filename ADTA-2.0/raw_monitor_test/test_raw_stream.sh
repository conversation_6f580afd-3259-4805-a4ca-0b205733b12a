#!/bin/bash

# Raw Stream Monitor 테스트 스크립트

API_BASE="http://localhost:9999"
DURATION=30
AUTH_TOKEN=""

echo "🚀 Raw Stream Monitor 테스트"
echo "================================"

# 함수 정의
test_status() {
    echo "📊 스트림 상태 확인..."
    curl -s "$API_BASE/api/raw-monitor/stream/status" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(f'✅ 상태 조회 성공:')
    print(f'   활성 연결: {data[\"active_connections\"]}/{data[\"max_connections\"]}')
    print(f'   버퍼 크기: {data[\"buffer_size\"]}')
    print(f'   최대 지속시간: {data[\"max_duration\"]}초')
    print(f'   인증 활성화: {data[\"auth_enabled\"]}')
except Exception as e:
    print(f'❌ 상태 조회 실패: {e}')
"
    echo
}

test_stream() {
    local duration=${1:-30}
    local auth_param=""
    
    if [ -n "$AUTH_TOKEN" ]; then
        auth_param="&auth_token=$AUTH_TOKEN"
    fi
    
    echo "📡 SSE 스트림 테스트 (${duration}초)..."
    echo "   URL: $API_BASE/api/raw-monitor/stream?max_duration=${duration}${auth_param}"
    echo "   패킷 수신 중... (Ctrl+C로 중지)"
    echo "----------------------------------------"
    
    # SSE 스트림 수신
    curl -s -N "$API_BASE/api/raw-monitor/stream?max_duration=${duration}${auth_param}" | while IFS= read -r line; do
        if [[ $line == data:* ]]; then
            # 'data: ' 제거
            packet_data="${line#data: }"
            
            # 시스템 메시지와 패킷 구분
            if [[ $packet_data == *"[STREAM_START]"* ]]; then
                echo "🟢 $packet_data"
            elif [[ $packet_data == *"[STREAM_END]"* ]]; then
                echo "🔴 $packet_data"
                break
            elif [[ $packet_data == *"[HEARTBEAT]"* ]]; then
                echo "💓 $packet_data"
            elif [[ $packet_data == *"[ERROR]"* ]]; then
                echo "❌ $packet_data"
            elif [[ $packet_data == *"]"* ]] && [[ $packet_data != *"["*"STREAM"* ]]; then
                # 실제 패킷 데이터
                echo "📦 $packet_data"
            fi
        fi
    done
    
    echo "----------------------------------------"
    echo "✅ 스트림 테스트 완료"
    echo
}

test_connection_limit() {
    echo "🔒 연결 제한 테스트..."
    
    # 백그라운드에서 3개 연결 시도
    for i in {1..4}; do
        echo "   연결 $i 시도..."
        
        # 백그라운드에서 연결 시도
        timeout 5 curl -s "$API_BASE/api/raw-monitor/stream?max_duration=10" > /dev/null &
        local pid=$!
        
        sleep 0.5
        
        # 4번째 연결은 실패해야 함
        if [ $i -eq 4 ]; then
            # 잠시 기다린 후 상태 확인
            sleep 1
            if kill -0 $pid 2>/dev/null; then
                echo "   ❌ 4번째 연결이 성공했습니다 (제한이 작동하지 않음)"
            else
                echo "   ✅ 4번째 연결이 거부되었습니다 (제한 정상 작동)"
            fi
        fi
    done
    
    # 백그라운드 프로세스 정리
    jobs -p | xargs -r kill 2>/dev/null
    echo "✅ 연결 제한 테스트 완료"
    echo
}

test_auth() {
    echo "🔐 인증 테스트..."
    
    # 인증 없이 접근
    echo "   토큰 없이 접근..."
    response=$(curl -s -w "%{http_code}" "$API_BASE/api/raw-monitor/stream?max_duration=5" -o /dev/null)
    
    if [ "$response" = "401" ]; then
        echo "   ✅ 인증 없는 접근 차단됨 (401)"
    elif [ "$response" = "200" ]; then
        echo "   ⚠️  인증이 비활성화되어 있습니다"
    else
        echo "   ❌ 예상치 못한 응답: $response"
    fi
    
    echo "✅ 인증 테스트 완료"
    echo
}

# 메인 실행
main() {
    echo "사용법: $0 [옵션]"
    echo "옵션:"
    echo "  -d DURATION  스트림 지속시간 (기본: 30초)"
    echo "  -t TOKEN     인증 토큰"
    echo "  -s           상태만 확인"
    echo "  -l           연결 제한 테스트"
    echo "  -a           인증 테스트"
    echo "  -h           도움말"
    echo
    
    # 옵션 파싱
    while getopts "d:t:slah" opt; do
        case $opt in
            d) DURATION="$OPTARG" ;;
            t) AUTH_TOKEN="$OPTARG" ;;
            s) test_status; exit 0 ;;
            l) test_connection_limit; exit 0 ;;
            a) test_auth; exit 0 ;;
            h) exit 0 ;;
            *) echo "잘못된 옵션: -$OPTARG" >&2; exit 1 ;;
        esac
    done
    
    # 기본 테스트 실행
    test_status
    test_stream "$DURATION"
}

# 스크립트가 직접 실행될 때만 main 함수 호출
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
