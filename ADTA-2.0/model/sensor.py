from enum import StrEnum, IntEnum
from pydantic import BaseModel


class Mode(StrEnum):
    PRODUCTION = "production"
    SEA_TRIAL = "seatrial"


class SensorName(StrEnum):
    GPS_1 = "gps1"
    GPS_2 = "gps2"
    GYRO_1 = "gyro1"
    GYRO_2 = "gyro2"
    ECDIS = "ecdis"


class CastType(StrEnum):
    UNICAST = "unicast"
    MULTICAST = "multicast"


class SensorConfig(BaseModel):
    """
    Represents the configuration for a specific sensor.

    Attributes:
        source (bytes): The source identifier for the GPS sensor.
        talker_identifier (bytes): The talker identifier in the NMEA sentence.
        sentence (bytes): The type of NMEA sentence to be used (e.g., GGA).
    """

    source: str
    talker_identifier: str
    sentence: str


class Sensor(BaseModel):
    """
    Represents the configuration for a GPS or Gyro sensor.

    Attributes:
        sensor_config (SensorConfig): The detailed configuration for the sensor.
        port_in (int): The port for incoming data.
        port_out (int): The port for outgoing data.
    """

    sensor_config: list[SensorConfig]
    destination: str
    port_out: int
