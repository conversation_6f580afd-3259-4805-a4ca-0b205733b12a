from typing import List, Optional
from enum import Enum
from pydantic import BaseModel, validator


class SearchStatus(str, Enum):
    """검색 상태 열거형 (장기 실행 안정성)"""

    SUCCESS = "success"
    FAILED = "failed"
    NO_DATA = "no_data"


class SearchMetadata(BaseModel):
    last_search_time: Optional[str] = None
    last_search_duration: Optional[int] = None
    last_search_total_messages: Optional[int] = None
    last_search_status: Optional[SearchStatus] = None  # 타입 안전성 강화
    last_search_updated_devices: Optional[int] = None


class DeviceSetting(BaseModel):
    available_ids: List[str]
    available_sentence: List[str]
    limit: int
    selected_61162_450: List[str] = []
    search_ids: List[str] = []

    @validator("selected_61162_450")
    def validate_selected(cls, v, values):
        limit = values.get("limit", 0)
        if len(v) > limit:
            raise ValueError(f"selected_61162_450 list too long (limit={limit}): {v}")
        return v


class AllDeviceSettings(BaseModel):
    metadata: Optional[SearchMetadata] = None
    devices: dict[str, DeviceSetting]
