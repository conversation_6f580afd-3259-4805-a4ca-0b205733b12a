import re
from pydantic import BaseModel
from enum import Enum
from typing import List, Optional
from pydantic.functional_validators import field_validator
from pydantic import FieldValidationInfo


class ProtocolVersion(Enum):
    V1 = "61162-1"
    V450 = "61162-450"


class SentenceType(str, Enum):
    """Sentence type enum."""

    parametric = "PARAMETRIC"
    encapsulation = "ENCAPSULATION"
    query = "QUERY"
    proprietary = "PROPRIETARY"
    tagblock = "TAGBLOCK"


# Rule for NMEA sentence filtering
class SentenceRule(BaseModel):
    """Rule for filtering sentences."""

    sentence: SentenceType | str
    ignore: bool = False
    expire_time: float = -1
    relay: Optional[str] = None  # "hostname:port" 형식, 없으면 None

    @field_validator("sentence", mode="before")
    @classmethod
    def validate_sentence_name(cls, value: str, info: FieldValidationInfo):
        """Validate sentence name."""
        return _validate_sentence_name(value)


class SourceRule(BaseModel):
    source_id: str  # ex: "GP0001", "GYR0002"
    ignore: bool = False
    expire_time: float = -1
    relay: Optional[str] = None  # "hostname:port" 형식, 없으면 None


class EndpointRule(BaseModel):
    ignore: bool = False
    relay: Optional[str] = None  # "hostname:port" 형식, 없으면 None


class ServerRules(BaseModel):
    endpoint_rules: EndpointRule = EndpointRule()
    source_rules: List[SourceRule] = []
    sentence_rules: List[SentenceRule] = []


class ServerConfig(BaseModel):
    # set multicast address ip and name manually
    name: str
    transport_protocol: str = "udp"
    message_protocol: ProtocolVersion = ProtocolVersion.V1
    port: int
    address: str = "0.0.0.0"


REGEX_SENTENCE_TYPE = re.compile(r"PARAMETRIC|ENCAPSULATION|QUERY|PROPRIETARY|TAGBLOCK")
REGEX_SENTENCE_NAME = re.compile(r"[A-Z]{3}|[A-Z]{5}")


def _validate_sentence_name(p: str) -> SentenceType | str:
    if REGEX_SENTENCE_TYPE.match(p):
        return SentenceType(p)
    if REGEX_SENTENCE_NAME.match(p):
        return p
    raise ValueError(f"Sensor configuration: Invalid sentence name: {p}")
